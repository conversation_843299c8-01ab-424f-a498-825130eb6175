var mongoose = require('mongoose');
var fromDB = 'mongodb://localhost:27017/winmed_office',
toDB = 'mongodb+srv://moorstech:<EMAIL>/winmed_office?retryWrites=true&w=majority';

let collections=['appointments','diagnoses','drugs','hospitals','invoices',"maladies","medicaments",'profiles','rooms','sessions','specialties',"supplies","timeoffs","users"];
let urlSource = "mongodb://localhost:27017/";
let dbSource = "winmed_office";
let MongoClientSource = require('mongodb').MongoClient;

let urlTarget = "mongodb+srv://moorstech:<EMAIL>/";
let dbTarget = "winmed_office";
let MongoClientTarget = require('mongodb').MongoClient;

let chunks = 100;
let timeOut = 7000;



for(let i=0;i<collections.length;i++){
   function copyDocumentsInChunks() {
    MongoClientSource.connect(urlSource, { useNewUrlParser: true }, function(error, mongo) {
        if (error) throw error;
        
        let db = mongo.db(dbSource);
        let collectionSource = collections[i];
        db.collection(collectionSource).find({}).sort({_id:1}).toArray(function(err, result) {
            if (err) throw err;
            MongoClientTarget.connect(urlTarget, { useNewUrlParser: true }, function(error, mongo) {
               if (error) throw error;
               
               let db = mongo.db(dbTarget);
               let collectionTarget = collections[i];
               let documents=result;
               db.collection(collectionTarget).insertMany(documents, function(err, result) {
                  // if(err) throw err;
                   mongo.close();
               });
           });
            
        });
    });
}

copyDocumentsInChunks()
}
