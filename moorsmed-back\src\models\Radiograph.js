import mongoose from "mongoose";
const Schema = mongoose.Schema;
import mongoosePaginate from 'mongoose-paginate';

const RadiographSchema = new mongoose.Schema({
    hospital: {
        type: Schema.Types.ObjectId,
        ref: 'Hospital'
    },
    name: {
        type: String,
        required:true,
    },
    radiographFamily: {
        type: Schema.Types.ObjectId,
        ref: 'RadiographFamily'
    },
    updatedBy: {
        type: Schema.Types.ObjectId,
        ref: 'Profile'
    },
    createdBy: {
        type: Schema.Types.ObjectId,
        ref: 'Profile'
    }
}, {
    timestamps: true,
    collation: { locale: 'fr', strength: 1 }
});
RadiographSchema.plugin(mongoosePaginate);
RadiographSchema.pre('find', populateRadiograoh);
RadiographSchema.pre('findOne', populateRadiograoh);
RadiographSchema.pre('findOneAndUpdate', populateRadiograoh);
function populateRadiograoh(next) {
    this.populate('radiographFamily')
    next();
}
module.exports = mongoose.model("Radiograph", RadiographSchema);