import Service from './Service';
import APIError from '../errors/APIError';
import mongoose from 'mongoose';
import Hospital from './../models/Hospital';
import {restructureProfileObj} from "../helpers/profileRework";
import Profile from "../models/Profile";


class SupplyService extends Service {
    constructor(model) {
        super(model);
        this.querySupply=this.querySupply.bind(this)
this.getSupplies = this.getSupplies.bind(this);
    this.createOrUpdateSupply = this.createOrUpdateSupply.bind(this);
    this.deleteSupplies = this.deleteSupplies.bind(this);
    }
    async querySupply(query={},filters, user) {
        //supplyIDs
        if (filters.supplyIDs) {
            query._id={$in:filters.supplyIDs}
        }
        //searchText
        if (filters.searchText) {
            if (!query["$and"]) query["$and"] = [];
            let or = [
                { name: { $regex: filters.searchText, $options: "i" } }, 
            ];
            if (filters.searchText.length == 12) or.push({ _id: mongoose.Types.ObjectId(filters.searchText) });
            query["$and"].push({ $or: or });
        }
        //type
        if (filters.types) {
            query.type={$in:filters.types}
        }
        //quantityMax
        if (filters.quantityMax) {
            query.quantity={$lte:filters.quantityMax,$gte:0}
        }
        //quantityMin
        if (filters.quantityMin) {
            query.quantity={$gte:filters.quantityMin,$gte:0}
        }
        //costPriceMax
        if (filters.costPriceMax) {
            query.costPrice={$lte:filters.costPriceMax,$gte:0}
        }
        //costPriceMin
        if (filters.costPriceMin) {
            query.costPrice={$gte:filters.costPriceMin,$gte:0}
        }
        //sellingPriceMax
        if (filters.sellingPriceMax) {
            query.sellingPrice={$lte:filters.sellingPriceMax,$gte:0}
        }
        //sellingPriceMin
        if (filters.sellingPriceMin) {
            query.sellingPrice={$gte:filters.sellingPriceMin,$gte:0}
        }
        
        return query;
    }
    async getSupplies(filters, user) {
        let query={hospital:mongoose.Types.ObjectId(user.profile.hospital._id), deletedAt: null};
        query=await this.querySupply(query,filters,user);
        let options = {
            sort: {type:-1,name:1,createdAt:1 },
            page: parseInt(filters.page, 10) || 1,
            limit: parseInt(filters.limit, 10) || 1000
        };
        let supplies = await this.model.paginate(query, options);
        if (!supplies) throw new APIError(404, 'cannot find supplies');
        supplies.docs = supplies.docs.map(s => {
            if(s.suuplier) s._doc.supplier = restructureProfileObj(s.supplier, false);
            return s
        })
    
        return supplies;
    }
    async createOrUpdateSupply(supply, supplyID =null, user) {
        supply.hospital=user.profile.hospital._id;
        supply.updatedBy=user.profile._id;
        if(!supplyID)        
        supply.createdBy=user.profile._id;
        let query={hospital:mongoose.Types.ObjectId(user.profile.hospital._id)};
        if(supply.supplier){
            const supplierProfile = await Profile.findOne({_id: supply.supplier});
            suppy.supplier = supplierProfile.supplier._id
        }
        if(supplyID){
            query._id=mongoose.Types.ObjectId(supplyID);
            supply=await this.model.findOneAndUpdate(query,supply,{new:true,upsert: true, setDefaultsOnInsert:true});
        }
        else {
            supply=await this.model.create(supply);
            supplyID=supply._id;
        }
        if (!supply) throw new APIError(404, 'cannot create supply');
        if(supply.type==="SESSION"){
            let hospital=await Hospital.findById(user.profile.hospital._id);
            if(!hospital.supplies)hospital.supplies=[];
            if(!hospital.supplies.some(x=>x._id+""===supply._id+""))
            await Hospital.findOneAndUpdate({_id:mongoose.Types.ObjectId(user.profile.hospital._id)},{$push:{sessions:supply._id}},{new:true})
        }
        return supply;
    }
    async deleteSupplies(filters, user) {
        let query={hospital:mongoose.Types.ObjectId(user.profile.hospital._id)};
        query=await this.querySupply(query,filters,user);
        let supplies = await this.model.softDeleteMany(query);
        if(filters.supplyIDs && filters.supplyIDs.length>0)
        await Hospital.findOneAndUpdate({_id:mongoose.Types.ObjectId(user.profile.hospital._id)},{$pull:{sessions:{$in:filters.supplyIDs}}},{new:true})

        if (!supplies) throw new APIError(404, 'cannot delete supplies');
        return supplies;
      }

}

export default SupplyService;