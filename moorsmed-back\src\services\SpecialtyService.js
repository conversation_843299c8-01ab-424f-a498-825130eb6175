import Service from './Service';
import APIError from '../errors/APIError';
import Hospital from '../models/Hospital';
import Session from '../models/Session';
import mongoose, { mongo } from 'mongoose';

class SpecialtyService extends Service {
    constructor(model) {
        super(model);
        this.createSpecialty = this.createSpecialty.bind(this);
        this.deleteSpecialty = this.deleteSpecialty.bind(this);
        this.editSpecialty = this.editSpecialty.bind(this);
        this.findOneSpecialty = this.findOneSpecialty.bind(this);
        this.findSpecialties = this.findSpecialties.bind(this);
    }
    async createSpecialty(specialty, user) {
        specialty.createdBy = user.profile._id;
        specialty.updatedBy = user.profile._id;
        specialty.hospital = user.profile.hospital._id;
        specialty = new this.model(specialty);
        specialty = await specialty.save();
        if (!specialty) throw new APIError(400, 'cannot create specialty');
        return specialty;
    }

    async deleteSpecialty(specialtyID, user) {
        let specialty = await this.model.findByIdAndDelete(specialtyID);
        if (!specialty) throw new APIError(400, 'cannot delete specialty');
        return specialty;
    }

    async editSpecialty(specialty, user) {
        specialty.updatedBy = user.profile._id;
        specialty = new this.model(specialty);
        specialty = await this.model.findByIdAndUpdate(specialty._id, specialty, { new: true });
        if (!specialty) throw new APIError(400, 'cannot update specialty');
        return specialty;
    }
    async findOneSpecialty(specialtyID, user) {
        let specialty = await this.model.findById(specialtyID);
        if (!specialty) throw new APIError(404, 'cannot find specialty');
        return specialty;
    }
    async findSpecialties(name, practices, user) {
        if(!user.profile.hospital.isManager){
            const managerHospitals = await Hospital.find({isManager: true});
            const hospitalIds = [...managerHospitals.map(h => h._id) , mongoose.Types.ObjectId(user.profile.hospital._id)];
            let query={hospital: {$in: hospitalIds}};
            if (name) query.name = { $regex: name, $options: 'i' };
            if (practices) query.practices = { $all: practices };
            let specialties = await this.model.find(query);
            if (!specialties) throw new APIError(404, 'cannot find specialties');
            return specialties;
        }else{
            const query = {hospital:  mongoose.Types.ObjectId(user.profile.hospital._id)};
            if (name) query.name = { $regex: name, $options: 'i' };
            if (practices) query.practices = { $all: practices };

            const specialtiesWithAppointments = await this.model.aggregate([
                {
                  $match: query
                },
                {
                  $lookup: {
                    from: "sessions",
                    let: { specialtyId: "$_id" },
                    pipeline: [
                        {
                            $lookup: {
                                from: 'appointments',
                                localField: 'appointment',
                                foreignField: '_id',
                                as: 'appointment',
                            },
                        },
                        {
                            $unwind: {
                              path: "$appointment",
                            },
                        },
                        {
                            $lookup: {
                                from: 'diagnoses',
                                localField: 'diagnoses',
                                foreignField: '_id',
                                as: 'diagnoses',
                            },
                        },
                        {
                            $match: {
                            $expr: {
                                $and: [
                                { $eq: ["$appointment.state", "COMPLETED"] },
                                { $in: ["$$specialtyId", "$diagnoses.specialty"] },
                                ],
                            },
                            },
                        },
                        {
                            $group: {
                            _id: null,
                            SessionCount: { $sum: 1 },
                            },
                        },
                        ],
                        as: "Sessions",
                    },
                },
                {
                  $unwind: {
                    path: "$Sessions",
                    preserveNullAndEmptyArrays: true,
                  },
                },
                {
                  $project: {
                    _id: 1,
                    name: "$name",
                    hospital: "$hospital",
                    content: "$content",
                    priority: "$priority",
                    practicesConfiguration: "$practicesConfiguration",
                    practices: "$practices",
                    createdBy: "$createdBy",
                    updatedBy: "$updatedBy",
                    createdAt: "$createdAt",
                    updatedAt: "$updatedAt",
                    SessionCount: { $ifNull: ["$Sessions.SessionCount", 0] },
                  },
                },
              ]);
        
            return specialtiesWithAppointments
        }
    }
}

export default SpecialtyService;