import Service from './Service';

class StaffService extends Service {
    constructor(model) {
        super(model);
        this.findProfiles = this.findProfiles.bind(this);
    }

    async findProfiles(filters, page, limit, user) {
        let query = { 
            hospital: mongoose.Types.ObjectId(user.profile.hospital._id),
            $or:[{title:"DOCTOR",_id:{$in:user.profile.doctors}},{isAdmin:true},{title:{$ne:"DOCTOR"}}]
        };
        //profileIDs
        if (filters.profileIDs) {
            query._id = { $in: filters.profileIDs };
        };
        //on name
        if (filters.name) {
            if (!query["$and"]) query["$and"] = [];
            query["$and"].push({ $or: [{ firstName: { $regex: filters.name, $options: "i" } }, { lastName: { $regex: filters.name, $options: "i" } }] });
        };

        //on gender
        if (filters.gender) query.gender = filters.gender;

        //profileNumber
        if (filters.profileNumber) query.profileNumber = { $regex: filters.profileNumber, $options: "i" };

        //email
        if (filters.email) query.email = { $regex: filters.email, $options: "i" };

        //isAdmin
        if (filters.isAdmin) query.isAdmin = filters.isAdmin;

        //titles
        if (filters.titles) {
            if (!query["$and"]) query["$and"] = [];
            filters.titles = filters.titles.map(x => Object.assign({}, {
                title: x
            }));
            query["$and"].push({ $or: filters.titles });
        }

        //positions
        if (filters.positions) {
            if (!query["$and"]) query["$and"] = [];
            filters.positions = filters.positions.map(x => Object.assign({}, {
                position: x
            }));
            query["$and"].push({ $or: filters.positions });
        }

        //residencies
        if (filters.residencies) {
            if (!query["$and"]) query["$and"] = [];
            filters.residencies = filters.residencies.map(x => Object.assign({}, {
                residency: x
            }));
            query["$and"].push({ $or: filters.residencies });
        }

        //seniorities
        if (filters.seniorities) {
            if (!query["$and"]) query["$and"] = [];
            filters.seniorities = filters.seniorities.map(x => Object.assign({}, {
                seniority: x
            }));
            query["$and"].push({ $or: filters.seniorities });
        };
        //searchText
        if (filters.searchText) {
            if (!query["$and"]) query["$and"] = [];
            let or = [{ firstName: { $regex: filters.searchText, $options: "i" } }, { lastName: { $regex: filters.searchText, $options: "i" } }, { email: { $regex: filters.searchText, $options: "i" } }, { assignedID: { $regex: filters.searchText, $options: "i" } }, { phoneNumber: { $regex: filters.searchText, $options: "i" } }];
            if (filters.searchText.length == 12) or.push({ _id: mongoose.Types.ObjectId(filters.searchText) });

            query["$and"].push({ $or: or });
        }

        let options = {
            sort: { title: 1, seniority: -1, firstName: 1, lastName: 1, startingDate: 1 },
            page: parseInt(page, 10) || 1,
            limit: parseInt(limit, 10) || 1000
        };

        let profiles = await this.model.paginate(query, options);
        if (!profiles) throw new APIError(404, 'cannot find profiles');
        return profiles;
    }
}
