import mongoose from "mongoose";
import { SPECIALTY_TYPES, PRACTICES_TYPES } from "../../config/utils/variables";

const Schema = mongoose.Schema;

const SpecialtySchema = new mongoose.Schema({
    hospital: {
        type: mongoose.Types.ObjectId,
        ref: 'Hospital',
        required: true
    },
    name: {
        type: String,
        unique: true
    },
    content: {
        type: String
    },
    priority: {
        type: Number
    },
    practicesConfiguration: {
        type: Array,
        default: SPECIALTY_TYPES
    },
    practices: {
        type: Array,
        default: PRACTICES_TYPES,
        uppercase: true
    },
    createdBy: {
        type: mongoose.Types.ObjectId,
        ref: 'Profile'
    },
    updatedBy: {
        type: mongoose.Types.ObjectId,
        ref: 'Profile'
    }

}, {
    timestamps: true,
    collation: { locale: 'fr', strength: 1 }
});
SpecialtySchema.pre('find', sortMiddlware);
function sortMiddlware(next) {
    this.sort({ priority: 1, name: 1 });
    next();
}

module.exports = mongoose.model("Specialty", SpecialtySchema);