import Service from './Service';
import APIError from '../errors/APIError';
import mongoose, { mongo } from 'mongoose';
import ProfileService from "../services/ProfileService";
import Profile from "../models/Profile";
const profileService = new ProfileService(Profile);
import SessionService from "../services/SessionService";
import Session from "../models/Session";
const sessionService = new SessionService(Session);
import socket from "../../config/socket";
import Invoice from "../models/Invoice";
import Supply from "../models/Supply";
import Diagnose from "../models/Diagnose";
import Timeoff from "../models/Timeoff";
import { getUtcDate, copyTime,getDateFromTime ,getDaysOfWeek} from "../helpers/dates";
import moment from "moment";
import Notification from "../models/Notification";
import jwt from 'jsonwebtoken';
import { sendEmail } from './../emails/sendEmail';
import {cache} from "./ReportingService";
import {resetCacheDates} from "../helpers/reporting";
import {getDatesAndDays} from "../helpers/dates";
import Patient from "../models/Patient";
import Staff from "../models/Staff";
import {restructureProfileObj} from "../helpers/profileRework";

class AppointmentService extends Service {
  constructor(model) {
    super(model);
    this.queryAppointment = this.queryAppointment.bind(this);
    this.getAppointments = this.getAppointments.bind(this);
    this.createAppointment = this.createAppointment.bind(this);
    this.updateAppointment = this.updateAppointment.bind(this);
    this.updateMultipleAppointment = this.updateMultipleAppointment.bind(this);
    this.getIntervalAppointment = this.getIntervalAppointment.bind(this);
    this.doctorView = this.doctorView.bind(this);
    this.ApprovedToInProgress = this.ApprovedToInProgress.bind(this);
    this.toCompleted=this.toCompleted.bind(this);
    this.waitingPatients=this.waitingPatients.bind(this);
    this.statesPerDay=this.statesPerDay.bind(this);
    this.switchAppointment=this.switchAppointment.bind(this);
    this.createUpdateTimeoff=this.createUpdateTimeoff.bind(this);
    this.deleteTimeoff=this.deleteTimeoff.bind(this);
    this.getTimeOffs=this.getTimeOffs.bind(this);
    this.timeProposition=this.timeProposition.bind(this);
    this.checkPlanning=this.checkPlanning.bind(this);
    this.estimateAppointmentDuration=this.estimateAppointmentDuration.bind(this);
    this.mixAppointmentsTimeoffs=this.mixAppointmentsTimeoffs.bind(this);
    this.checkBeforeTimeoff=this.checkBeforeTimeoff.bind(this);
    this.deleteTimeoffFix=this.deleteTimeoffFix.bind(this);
    this.getContentOfTimeoff=this.getContentOfTimeoff.bind(this);
    this.FirstAppointmentForPatient=this.FirstAppointmentForPatient.bind(this);
    this.sendToPatient=this.sendToPatient.bind(this);
    this.doctorBadgeStat=this.doctorBadgeStat.bind(this);
    this.updatePatientSessionsCount=this.updatePatientSessionsCount.bind(this);


  }
  async queryAppointment(filters, query = {}, user) {

     // date range
     if (filters.startDate || filters.endDate) {
      let dateQuery = {};
      if(filters.startDate) dateQuery["$gte"] = new Date(filters.startDate);
      if(filters.endDate) dateQuery["$lte"] = new Date(filters.endDate);
      query.date = dateQuery;
    }

    //date
    if (filters.date) {
        query.date = new Date(filters.date);
    }
    
    //startTime
    if (filters.startTime) {
      query.startTime = { $gte: new Date(filters.startTime) };
    }
    //endTime
    if (filters.endTime) {
      query.endTime = { $lte: new Date(filters.endTime) };
    }
    //type
    if (filters.types) {
      query.type = { $in: filters.types };
    }
    //state
    if (filters.states) {
      query.state = { $in: filters.states };
    }
    //patients
    if (filters.patient) {
      let patientIDs = await profileService.findProfiles({ titles: ['PATIENT'], searchText: filters.patient }, 1, 1000, user);
      patientIDs = Array.from(patientIDs.docs).map(x => x._doc.patientId);
      if (filters.patient.length == 12){
        const profile = await Profile.findOne({_id : mongoose.Types.ObjectId(filters.patient)})
        patientIDs.push(profile.patient._id);
      } 
      query.patient = { $in: patientIDs };
    }
    //doctor
    if (filters.doctor) {
      let doctorIDs = await profileService.findProfiles({ titles: ['DOCTOR'], searchText: filters.doctor }, 1, 1000, user);
      doctorIDs = Array.from(doctorIDs.docs).map(x => x._doc.staffId);
      if (filters.doctor.length == 12){
        const profile = await Profile.findOne({_id : mongoose.Types.ObjectId(filters.doctor)})
        doctorIDs.push(profile.staff._id);
      }

      query.doctor = { $in: doctorIDs };
    }
    //appointmentsIDs
    if (filters.appointmentIDs) {
      query._id = { $in: filters.appointmentIDs };
    }
    return query;
  }

  async getAppointments(filters, page, limit, user) {
    let query = { hospital: mongoose.Types.ObjectId(user.profile.hospital._id) };
    let doctors=user.profile.staff.doctors;

    // Check if user is a receptionist
    if(user.profile.title === 'RECEPTIONIST') {
      // If no doctors filter is provided, show all hospital doctors
      if(!filters.doctors || filters.doctors.length === 0) {
        // Get all doctors in the hospital for receptionists
        const allHospitalDoctors = await Staff.find({
          hospital: mongoose.Types.ObjectId(user.profile.hospital._id),
          title: 'DOCTOR',
          deletedAt: null
        });
        doctors = allHospitalDoctors.map(s => s._id);
      } else {
        // If specific doctors are requested, filter them
        const staffDoctors  = await Staff.find({profile : {$in : filters.doctors}});
        const staffDoctorsIDs = staffDoctors.map(s => s._id+"");
        // For receptionists, allow any hospital doctor (no restriction to assigned doctors)
        const allHospitalDoctors = await Staff.find({
          hospital: mongoose.Types.ObjectId(user.profile.hospital._id),
          title: 'DOCTOR',
          deletedAt: null
        });
        const allHospitalDoctorIds = allHospitalDoctors.map(s => s._id+"");
        doctors = staffDoctorsIDs.filter(x => allHospitalDoctorIds.some(y => y+""==x+""));
      }
    } else {
      // For non-receptionists, use existing logic
      if(filters.doctors){
        const staffDoctors  = await Staff.find({profile : {$in : filters.doctors}});
        const staffDoctorsIDs = staffDoctors.map(s => s._id+"");
        doctors = staffDoctorsIDs.filter(x=>user.profile.staff.doctors.some(y=>y+""==x+""));
      }
    }
    query.doctor={$in:doctors}
  
    query = await this.queryAppointment(filters, query, user);
    let options = {
      sort: { date: 1,stateOrder:-1, startTime: 1, endTime: 1, state: 1, type: 1, firstName: 1, lastName: 1, startingDate: 1 },
      page: parseInt(page, 10) || 1,
      limit: parseInt(limit, 10) || 1000,
    };

    let appointments = await this.model.paginate(query, options); 
    if (!appointments) throw new APIError(404, 'cannot find appointments');
    appointments.docs=appointments.docs.map(x=>{
      x._doc.doctor = restructureProfileObj(x.doctor, false);
      x._doc.patient = restructureProfileObj(x.patient , false);
      return x;
    })
    let invoices=await Invoice.find({hospital:mongoose.Types.ObjectId(user.profile.hospital._id), appointment:{$in:appointments.docs.map(x=>x._id)}});
    if(invoices.length>0){
      appointments.docs=appointments.docs.map(x=>{
        let invoice=invoices.find(w=>w.appointment+""==x._id+"");
        if(invoice){
          let invoiceObj = Object.assign({} , invoice._doc);
          let sessionObj = Object.assign({} , invoice.session._doc)
          invoiceObj.seller = restructureProfileObj(invoice.seller , true);
          invoiceObj.buyer = restructureProfileObj(invoice.buyer  , true);
          sessionObj.doctor = restructureProfileObj(invoice.session.doctor  , false);
          sessionObj.patient = restructureProfileObj(invoice.session.patient , false);
          invoiceObj.session = sessionObj;
          x._doc.invoice=invoiceObj;
        } 
        return x;
      })
    }
    return appointments;
  }

  async createAppointment(appointment, user,socketID,hostname) {
    if (user) {
      appointment.hospital = user.profile.hospital._id;
      appointment.createdBy = user.profile._id;
      appointment.updatedBy = user.profile._id;
      if (!appointment.state) appointment.state = "APPROVED";
    } else {
      appointment.state = "PENDING";
    }

    if(appointment.doctor){
      const doctorProfile = await Profile.findOne({_id: appointment.doctor});
      appointment.doctor = doctorProfile.staff._id
    }

    if(appointment.patient){
      const patientProfile = await Profile.findOne({_id: appointment.patient});
      appointment.patient = patientProfile.patient._id
    }

    appointment = new this.model(appointment);
    appointment = await appointment.save().then(doc => doc.populate('supply','name sellingPrice avgDuration').populate({path : 'patient' , populate: {path : "profile"}}).populate({path : 'doctor' , populate: {path : "profile"}}).execPopulate());    
    if (!appointment) throw new APIError(403, 'cannot create appointment');
    appointment.doctor._doc =  restructureProfileObj(appointment.doctor , false)
    appointment.patient._doc =  restructureProfileObj(appointment.patient , false)
    let session = {};
    if (appointment.state == "INPROGRESS"){
      session = await this.ApprovedToInProgress(appointment, user,socketID);
      resetCacheDates(cache,[[session.date, session.date]], user.profile.hospital._id);
    }
    socket.socket.emitToRoom(user.profile.hospital._id,'appointment-create',{profile:user.profile,appointment,operation:'create',socketID});
    socket.socket.emitToRoom(user.profile.hospital._id,'header-update',{profile:user.profile});
    this.sendToPatient(appointment,hostname,user);

    return { appointment, session };
  }

  async updateAppointment(appointmentID, appointment, user,socketID,hostname) {
    appointment.updatedBy = user.profile._id;
    let oldState = await this.model.findById(appointmentID).distinct('state');
    delete appointment.createdBy;
    let query = { hospital: mongoose.Types.ObjectId(user.profile.hospital._id), _id: mongoose.Types.ObjectId(appointmentID) };

    if(appointment.waitingTime>0){
      if(!appointment.patientArrived || appointment.state=="APPROVED" || appointment.state=="CANCELED" || appointment.state=="PENDING" ) appointment.waitingTime=0;
      else {
        appointment.waitingTime=(Date.now()-new Date(appointment.patientArrived).getTime())/(1000*60);
      }
    }
    
    if(appointment.doctor){
      const doctorProfile = await Profile.findOne({_id: appointment.doctor});
      appointment.doctor = doctorProfile.staff._id
    }

    if(appointment.patient){
      const patientProfile = await Profile.findOne({_id: appointment.patient});
      appointment.patient = patientProfile.patient._id
    }

    appointment = await this.model.findOneAndUpdate(query, appointment, { new: true });
    if (!appointment) throw new APIError(403, 'cannot update appointment');
    let session = {};
    if (oldState && (appointment.state == "INPROGRESS" || appointment.state == "COMPLETED") && oldState[0] !== appointment.state) {
      session = await this.ApprovedToInProgress(appointment, user,socketID);
      resetCacheDates(cache,[[session.date, session.date]], user.profile.hospital._id)
    }else {
      session = Object.assign( {} , appointment._doc);
      delete session._id;
      session=await Session.findOneAndUpdate({appointment:mongoose.Types.ObjectId(appointment._id),hospital:mongoose.Types.ObjectId(user.profile.hospital._id)}, session, { new: true });
    }
    if(appointment.state == "COMPLETED" && session){
      const profile = await sessionService.updateProfileFromSession(session._id , user);
    }
    appointment._doc.doctor =  restructureProfileObj(appointment.doctor , false);
    appointment._doc.patient =  restructureProfileObj(appointment.patient , false);
    
    socket.socket.emitToRoom(user.profile.hospital._id,appointment._id+"",{profile:user.profile,appointment,operation:'update',oldState:oldState[0],socketID});
    socket.socket.emitToRoom(user.profile.hospital._id,'header-update',{profile:user.profile});
    this.updatePatientSessionsCount(appointment.patient.patientId,user);
    if(session){ 
      session._doc.doctor =  restructureProfileObj(session.doctor  , false);
      session._doc.patient =  restructureProfileObj(session.patient , false);
    }

    //this.sendToPatient(appointment,hostname,user);
    return { appointment, session };
  }

  async updateMultipleAppointment(filters, appointment, user) {
    appointment.updatedBy = user.profile._id;
    delete appointment.createdBy;
    let query = await { hospital: mongoose.Types.ObjectId(user.profile.hospital._id) };
    query = this.queryAppointment(filters, query, user);
    let appointments = await this.model.updateMany(query, appointment);
    if (!appointments) throw new APIError(403, 'cannot update appointments');
    return appointments;
  }
  async getIntervalAppointment(filters, user) {
    let query = {
      hospital: mongoose.Types.ObjectId(user.profile.hospital._id),
      state: { $in: ['INPROGRESS', 'APPROVED'] }
    };
    if (filters.date) query.date = new Date(filters.date);

    if (filters.doctor) query.doctor = mongoose.Types.ObjectId(filters.doctor);
    let appointments = await this.model.find(query).sort({ startTime: 1 });
    if (!appointments) throw new APIError(404, 'error with filtering ');
    return {
      first: appointments[0],
      last: appointments[appointments.length - 1],
      before: filters.time ? appointments.filter(x => new Date(x.startTime).getTime() <= new Date(filters.time).getTime()).sort((a, b) => new Date(b.startTime).getTime() - new Date(a.startTime).getTime())[0] : null,
      after: filters.time ? appointments.filter(x => new Date(x.startTime).getTime() > new Date(filters.time).getTime()).sort((a, b) => new Date(a.startTime).getTime() - new Date(b.startTime).getTime())[0] : null
    };
  }

  async doctorView(date, user) {
    let query = {
      state: { $in: ['COMPLETED', 'INPROGRESS', 'APPROVED','ALMOST_COMPLETED'] },
      hospital: mongoose.Types.ObjectId(user.profile.hospital._id),
      date: new Date(date)
    };
    let sessionQuery = Object.assign({doctor : mongoose.Types.ObjectId(user.profile._id)},query);
    let appointmentQuery = Object.assign({doctor : mongoose.Types.ObjectId(user.profile.staff._id)},query)
    let appointments = await this.model.find(appointmentQuery).sort({ startTime: 1 });
    if (!appointments) throw new APIError(404, 'error with filtering ');
    appointments = appointments.map(a => {
      a._doc.doctor =  restructureProfileObj(a.doctor , false);
      a._doc.patient =  restructureProfileObj(a.patient , false);
      return a;
    })
    let sessions = await sessionService.getSessions(sessionQuery, 1, 1000, user);
    sessions = Array.from(sessions.docs);

    let AvgWaitingTime = sessionService.AvgWaitingTime(sessions);
    return {
      AvgWaitingTime,
      patientsWaitingNumber: appointments.filter(x => x.state == 'APPROVED').length,
      nextAppointment: appointments.filter(x=>x.state=="APPROVED")[0]||null
    };
  }
  async ApprovedToInProgress(appointment, user,socketID) {
    let session = await sessionService.createSession({}, appointment, user);
    let notification=await new Notification({
      profile:user.profile._id,
      receiver:session.doctor.profile._id,
      type:'APPOINTMENT',
      hospital:user.profile.hospital._id,
      title:"Nouveau rendez-vous en cours",
      content:" vous a associé à un rendez-vous en cours"
    }).save();
    
    socket.socket.emitToRoom(user.profile.hospital._id,notification.receiver+"-notification",{notification,profile:user.profile,socketID});
    socket.socket.emitToRoom(user.profile.hospital._id,'header-update',{profile:user.profile});

    return session;
  }
  async toCompleted(appointmentID, user,socketID) {
    let query={
      hospital:mongoose.Types.ObjectId(user.profile.hospital._id),
      _id:mongoose.Types.ObjectId(appointmentID),
      state:"ALMOST_COMPLETED"
    }
    let appointment = await this.model.findOneAndUpdate(query,{state:"COMPLETED"},{new:true});
    if (!appointment) throw new APIError(403, 'cannot update appointment');

    const session = await Session.findOne({hospital: mongoose.Types.ObjectId(user.profile.hospital._id), appointment: mongoose.Types.ObjectId(appointment._id)});
    if(session){
      const profile = await sessionService.updateProfileFromSession(session._id , user);
    } 

    socket.socket.emitToRoom(user.profile.hospital._id,appointment._id+"",{profile:user.profile,appointment,operation:'update',socketID});
    socket.socket.emitToRoom(user.profile.hospital._id,'header-update',{profile:user.profile});

    Invoice.findOneAndUpdate({
      hospital:mongoose.Types.ObjectId(user.profile.hospital._id),
      _id:mongoose.Types.ObjectId(appointmentID)
    },
    {closed:true,updatedBy:user.profile._id}
    ,{new:true,upsert: true, setDefaultsOnInsert:true});
    this.editStats(appointmentID,user);

    this.updatePatientSessionsCount(appointment.patient._id || appointment.patient,user);

    return appointment;
  }
  async waitingPatients(filters, user) {
    let query={
      hospital:mongoose.Types.ObjectId(user.profile.hospital._id),
      date:new Date(filters.date),
      state:{$in:filters.states}
    }
    let appointments = await this.model.find(query);
    if (!appointment) throw new APIError(403, 'cannot find appointment');


    return appointments.length;
  }

  async editStats(appointmentID,user){
    let query={
      hospital:mongoose.Types.ObjectId(user.profile.hospital._id),
      appointment:mongoose.Types.ObjectId(appointmentID),
    };
    let session=await Session.findOne(query).lean();
    let invoice=await Invoice.findOne(query).lean();
    if(invoice)
    // await this.editSuppliesStats(invoice,user);
    await this.editDiagnosesStats(session,user);
  }

  async editSuppliesStats(invoice,user){
    for (let i=0;i<invoice.items.length;i++){
      if(invoice.items[i].supply._id){
         await Supply.findOneAndUpdate({_id:mongoose.Types.ObjectId(invoice.items[i].supply._id)},{$inc: {
          consumedNumber: invoice.items[i].quantity,
        }})
      }
    }
  }
  async editDiagnosesStats(session,user){
      let sessions=await Session.find(
        {
          diagnoses:{$in:session.diagnoses.map(x=>x._id)},
        hospital:mongoose.Types.ObjectId(session.hospital._id),
        patient:mongoose.Types.ObjectId(session.patient._id),
        date:{$lte:new Date(session.date)}
      }
        );
        for(let i=0;i<session.diagnoses.length;i++){
          let diagnose=session.diagnoses[i];
          let sessionsNumber=1, patientNumber=1;
          let already=sessions.filter(ddd=>ddd.diagnoses.some(ttt=>ttt._id+""==diagnose._id+""));
          if(already>1) patientNumber=0;
           diagnose= await Diagnose.findOneAndUpdate({_id:mongoose.Types.ObjectId(diagnose._id)},{$inc: {
            patients: patientNumber,
            sessions:sessionsNumber
          }})
        }
  }
  async statesPerDay(filters,user){
    let query={
      hospital:mongoose.Types.ObjectId(user.profile.hospital._id),
      date:new Date(filters.date),
      doctor:{$in:user.profile.staff.doctors}
    };
    let appointments=await this.model.find(query).select('state startTime waitingTime patientArrived');

    let waitingAppointments=appointments.filter(x=>x.state=="APPROVED" || x.state=="INPROGRESS").length;
    let doneAppointments=appointments.filter(x=>x.state=="COMPLETED" || x.state=="ALMOST_COMPLETED" ).length;
    let averageWaitingTime=0,numberOfSessionStarted=0;
    let countWhoWaited=0;
    appointments.filter(x=>  x.patientArrived).map(x=>{
      countWhoWaited++;
      averageWaitingTime=averageWaitingTime+x.waitingTime;
    })

    if(countWhoWaited>0)
    averageWaitingTime=averageWaitingTime/countWhoWaited;
     let waitingPatients=0;
     waitingPatients=appointments.filter(x=>x.state=="APPROVED" && x.patientArrived).length
    return {waitingAppointments,doneAppointments,averageWaitingTime,waitingPatients};
  }

  async switchAppointment(appointmentID1,appointmentID2,user,socketID){
    let appointments=await this.model.find({
      hospital:mongoose.Types.ObjectId(user.profile.hospital._id),
      _id:{$in:[appointmentID1,appointmentID2]},
      state:'APPROVED'
    });

    if(!appointments || appointments.length!==2) throw new APIError(403, 'appointment not approved');
    let date1=appointments[0].startTime,date2=appointments[1].date;
    appointments[0].date=date2;
    appointments[1].date=date1;
    appointments[0].updatedBy=user.profile._id;
    appointments[1].updatedBy=user.profile._id;
    await appointments[0].save();
    await appointments[1].save();
    socket.socket.emitToRoom(user.profile.hospital._id,appointments[0]._id+"",{profile:user.profile,appointment:appointments[0],operation:'update',socketID});
    socket.socket.emitToRoom(user.profile.hospital._id,appointments[1]._id+"",{profile:user.profile,appointment:appointments[1],operation:'update',socketID});
    socket.socket.emitToRoom(user.profile.hospital._id,'header-update',{profile:user.profile});

    return appointments;
  }
  async createUpdateTimeoff(timeoff,editAppointments=false,fromDaily=true,user,socketID){
    let timeoffBefore;
    timeoff.hospital=user.profile.hospital._id;
    timeoff.updatedBy=user.profile._id;
    let operation='update';
    if(timeoff.doctor){
      const doctorProfile = await Profile.findOne({_id: timeoff.doctor});
      timeoff.doctor = doctorProfile.staff._id   
    }
    if(!timeoff._id){
      timeoff.createdBy=user.profile._id;
      timeoff=new Timeoff(timeoff);
      timeoff=await timeoff.save().then(doc => doc.populate({
        path: 'doctor',
        populate : [{path: "profile"}]
      }).execPopulate());
      operation='create';
      if (!timeoff) throw new APIError(404, 'cannot update timeoff');
    }
    else {
      let query={hospital:mongoose.Types.ObjectId(user.profile.hospital._id),_id:mongoose.Types.ObjectId(timeoff._id)}
      timeoffBefore=await Timeoff.findOne(query);
      timeoff=await Timeoff.findOneAndUpdate(query,timeoff,{new:true,upsert: true, setDefaultsOnInsert:true});
      if (!timeoff) throw new APIError(404, 'cannot update timeoff');
    }
    if(operation=="update"){
      socket.socket.emitToRoom(user.profile.hospital._id,timeoff._id+"",{profile:user.profile,timeoff,operation,socketID});
    }
    else if(operation=="create"){
      socket.socket.emitToRoom(user.profile.hospital._id,"timeoff-create",{profile:user.profile,timeoff,operation,socketID});
    }
    if(timeoff.isActive &&  timeoff.endTime && editAppointments){
      this.afterTimeOff(timeoff,user,socketID);
    }
    if(timeoff.createdFrom) socket.socket.emitToRoom(user.profile.hospital._id,timeoff.createdFrom+"",{profile:user.profile,timeoff,operation,socketID});
    let content=this.getContentOfTimeoff(null,timeoff,operation,user.profile.hospital.language);
    const profile = await Profile.findOne({_id: user.profile._id});
    const staff =  await profile.staff.populate("receptionits").execPopulate();
    for(let i=0;i<staff.receptionits.length;i++){
      let notification=await new Notification({
        profile:user.profile._id,
        receiver:staff.receptionits[i]._id,
        type:'TIMEOFF',
        hospital:user.profile.hospital._id,
        title:content,
        content:content
      }).save();
      socket.socket.emitToRoom(user.profile.hospital._id,notification.receiver+"-notification",{notification,profile:user.profile,socketID});
    }
    timeoff._doc.doctor =  restructureProfileObj(timeoff.doctor , false);

    return timeoff;
  }
  async deleteTimeoff(timeoffID,createdFromID=null,user,socketID){
    let query={
      hospital:mongoose.Types.ObjectId(user.profile.hospital._id),
      _id:mongoose.Types.ObjectId(timeoffID)
    }
    let operation='delete';
    let timeoff={deleted:true}
    let timeoffBefore=await Timeoff.findOne(query);

    if(createdFromID){
      timeoff.createdFrom=mongoose.Types.ObjectId(createdFromID)
    }
  
    timeoff=await Timeoff.findOneAndUpdate(query,timeoff,{new:true,upsert: true, setDefaultsOnInsert:true});
    socket.socket.emitToRoom(user.profile.hospital._id,timeoffID+"",{profile:user.profile,timeoff,operation,socketID});
    socket.socket.emitToRoom(user.profile.hospital._id,"timeoff-delete",{profile:user.profile,timeoff,operation,socketID});
    let content=this.getContentOfTimeoff(timeoffBefore,timeoff,operation,user.profile.hospital.language);
    const profile = await Profile.findOne({_id: user.profile._id});
    const staff =  await profile.staff.populate("receptionits").execPopulate();
    for(let i=0;i<staff.receptionits.length;i++){
      let notification=await new Notification({
        profile:user.profile._id,
        receiver:staff.receptionits[i]._id,
        type:'TIMEOFF',
        hospital:user.profile.hospital._id,
        title:content,
        content:content
    }).save();
      
      socket.socket.emitToRoom(user.profile.hospital._id,notification.receiver+"-notification",{notification,profile:user.profile,socketID});
    }
    return timeoff;
  }
  async deleteTimeoffFix(timeoffID,user,socketID){
    let query={
      hospital:mongoose.Types.ObjectId(user.profile.hospital._id),
      _id:mongoose.Types.ObjectId(timeoffID)
    }
    let operation='delete';
    let timeoff=await Timeoff.findOne(query);
    if (!timeoff || (!timeoff.isDaily && timeoff.doctor._id+""!==user.profile._id+"")) throw new APIError(404, 'cannot delete timeoff');


        timeoff=await Timeoff.findOneAndDelete(query);
    return timeoff;
  }
  async getTimeOffs(filters,user){
    let query={
      hospital:mongoose.Types.ObjectId(user.profile.hospital._id),
    };
    query['$or']=[{doctor:{ $exists: false}},{doctor:null}];
    let doctors=user.profile.staff.doctors;
    if(filters.doctors){
      const staffDoctors  = await Staff.find({profile : {$in : filters.doctors}});
      const staffDoctorsIDs = staffDoctors.map(s => s._id+"");
      doctors=staffDoctorsIDs.filter(x=>user.profile.staff.doctors.some(y=>y+""==x+""))
    }
    query['$or'].push({doctor:{$in:doctors}})
    let timeoffs,dailyBreak;
    if(filters.day){
      query.day=Number(filters.day);
      timeoffs=await Timeoff.find(query)
      dailyBreak=timeoffs.find(x=>x.type=="DAILY_BREAK");
      if(!dailyBreak) {dailyBreak=new Timeoff({
        hospital:user.profile.hospital._id,
        type:'DAILY_BREAK',
        day:Number(filters.day),
        startTime:getDateFromTime('2000-01-02',user.profile.hospital.schedules.find(x=>x.day+''==Number(filters.day)+'').startBreak),
        endTime:getDateFromTime('2000-01-02',user.profile.hospital.schedules.find(x=>x.day+''==Number(filters.day)+'').endBreak),
        description:'PAUSE',
        isWeekly:true,
        isActive:true,
      });
      dailyBreak=await dailyBreak.save();
      timeoffs.push(dailyBreak);
    }
      /*if(dailyBreak){
        timeoffs=timeoffs.filter(x=>x._id+""!==dailyBreak._id+"")
      }*/
    }else if(filters.date || filters.startDate || filters.endDate){
      let result=await this.addFixTimeoffs(filters,user);
      if(result.timeoffs)timeoffs=result.timeoffs;
      if(result.dailyBreak)dailyBreak=result.dailyBreak;
    }
    else {
      throw new APIError(404, 'u should provide date or day ');
    }
    timeoffs=timeoffs.filter(x=>!x.deleted);
    timeoffs = timeoffs.map(t => {
      if(t.doctor){
        t._doc.doctor = restructureProfileObj(t.doctor , false)
      }
      return t
    })

    return {timeoffs,dailyBreak};
  }
  async addFixTimeoffs(filters,user){
    let timeoffs,dailyBreak;
    let query={
      hospital:mongoose.Types.ObjectId(user.profile.hospital._id),
    };
    query['$or']=[{doctor:{ $exists: false}},{doctor:null}];
    let doctors=user.profile.staff.doctors;
    if(filters.doctors){
      const staffDoctors  = await Staff.find({profile : {$in : filters.doctors}});
      const staffDoctorsIDs = staffDoctors.map(s => s._id+"");
      doctors=staffDoctorsIDs.filter(x=>user.profile.staff.doctors.some(y=>y+""==x+""))
    }
    query['$or'].push({doctor:{$in:doctors}})
    if(!filters.date && (!filters.startDate || !filters.endDate)) throw new APIError(404, 'Dates should be specified');
    const startDate = filters.startDate ? filters.startDate : filters.date;
    const endDate = filters.endDate ? filters.endDate : filters.date;
    const {dates , days} = getDatesAndDays(startDate,endDate);

    let fixQuery=query;

    query.date={$gte : new Date(startDate) , $lte: new Date(endDate)};
    timeoffs=await Timeoff.find(query);

    delete fixQuery.date;
    fixQuery.day={$in : days};
    fixQuery.deleted={$nin:[true]};
    let fixTimes=await Timeoff.find(fixQuery); 

    let newTimes=[];
    dates.map(date => {
      const day = new Date(date).getDay();
      const dayFixTimeoffs = fixTimes.filter(ft => ft.day == day);
      dayFixTimeoffs.map(x=>{
        if(timeoffs.every(w=>w.createdFrom+""!==x._id+"")){
          newTimes.push(new Timeoff({
            doctor:x.doctor,
            date:new Date(date),
            isWeekly:false,
            createdFrom:x._id,
            type:x.type,
            description:x.description,
            startTime:copyTime(new Date(date),x.startTime),
            endTime:copyTime(new Date(date),x.endTime),
            isActive:x.isActive,
            hospital:user.profile.hospital._id
          }))
        }
      });
    })
    timeoffs=timeoffs.concat(newTimes);
    let dailyFix=fixTimes.find(x=>x.type=="DAILY_BREAK" && !x.doctor);
    if(dailyFix){
    dailyBreak=timeoffs.find(x=>x.createdFrom+""==dailyFix._id+"")
    //if(dailyBreak) timeoffs=timeoffs.filter(x=>x._id+""!==dailyBreak._id+"");
    }
    return {timeoffs,dailyBreak};
  }

  async afterTimeOff(timeoff,user,socketID){
    let query={
      startTime:{$gte:new Date(timeoff.startTime)},
      date:new Date(timeoff.date),
      hospital:mongoose.Types.ObjectId(timeoff.hospital),
      doctor:mongoose.Types.ObjectId(timeoff.doctor._id),
      state:{$in:['APPROVED']}
    }
    let appointments=await this.model.find(query).sort({startTime:1});
    for (let i=0;i<appointments.length;i++){
      let appointment=appointments[i];
      if(i==0 && appointments[0]) {
        appointments[0].startTime=new Date(timeoff.endTime);
        appointments[0]=await appointments[0].save();

      }
      else if(i>0 &&  appointment[i]) {
        let duration=this.estimateAppointmentDuration(appointment,user);
        if(new Date(appointment.startTime).getTime()-new Date(appointments[i-1].startTime).getTime()<duration){
          appointments[i]=new Date(new Date(appointments[i-1].startTime).getTime()+(duration*60*1000));
          appointments[i]=await appointments[i].save();
        }
        else break;
      }

    }
    if(appointments.length>0){
      appointments.map(x=>socket.socket.emitToRoom(user.profile.hospital._id,x._id+"",{profile:user.profile,x,socketID}));
      socket.socket.emitToRoom(user.profile.hospital._id,'header-update',{profile:user.profile});

    }
  }
  async timeProposition(filters,user,appointments=null){
    if(!filters.date) throw new APIError(404, 'date not defined');
    if(!appointments)
    appointments=await this.mixAppointmentsTimeoffs(filters,user);
    let suggestions=[];
    let startTime,endTime,foundTime=false,dayFull=false;
    let dayOfWeek=new Date(filters.date).getDay();
    dayOfWeek=user.profile.hospital.schedules.find(x=>x.day+""==dayOfWeek+"");
    let hositalStart=moment(moment(filters.date).format('YYYY-MM-DD') + ' ' + dayOfWeek.startTime).toDate();
    let hospitalEnd=moment(moment(filters.date).format('YYYY-MM-DD') + ' ' + dayOfWeek.endTime).toDate();
    startTime=hositalStart;
    let duration=this.estimateAppointmentDuration(filters,user);
    endTime=new Date(new Date(startTime).getTime() + duration*60000);
    if(appointments.length === 0) suggestions.push({time:startTime,inWorkHours: true})
    for(let i=0;i<appointments.length;i++){
        if(new Date(appointments[i].startTime).getTime()> new Date(endTime).getTime() && new Date(appointments[i].startTime).getTime()> new Date(startTime).getTime() ){
          foundTime=true;
          suggestions.push({time:startTime,inWorkHours:(new Date(startTime).getTime()<new Date(hospitalEnd).getTime() && new Date(startTime).getTime() >= new Date(hositalStart).getTime())});
                }
      startTime=new Date(new Date(appointments[i].endTime).getTime() + 1*60000);
      filters={
        doctor:appointments[i].doctor,
        supply:appointments[i].supply
      }
      duration=this.estimateAppointmentDuration(filters,user);
      endTime=new Date(new Date(startTime).getTime() + duration*60000);
      if(i==appointments.length-1)          
       suggestions.push({time:startTime,inWorkHours:(new Date(startTime).getTime()<new Date(hospitalEnd).getTime() && new Date(startTime).getTime() >= new Date(hositalStart).getTime())});

    }
    dayFull=suggestions.every(x=>!x.inWorkHours);
    suggestions=suggestions.slice(0,19);
    return {suggestions,dayFull,foundTime}
  }
  async checkPlanning(filters,user){
    if(!filters.time) throw new APIError(404, 'time not defined');
    let conflits=[];
    let time=new Date(filters.time);
    let date=getUtcDate(time);
    filters.date=date;
    let duration=this.estimateAppointmentDuration(filters,user);
    let endTime=filters.endTime;
    if(!endTime) endTime=new Date(new Date(time).getTime() + duration*60000);
    let appointments=await this.mixAppointmentsTimeoffs(filters,user);
    let beforeAppointments=appointments.filter(x=>new Date(x.startTime).getTime()<=new Date(time).getTime()).sort((a,b)=>new Date(b).getTime()-new Date(a).getTime());
    let afterAppointments=appointments.filter(x=>new Date(x.startTime).getTime()>=new Date(time).getTime()).sort((a,b)=>new Date(a).getTime()-new Date(b).getTime());
    let beforeAppointment,afterAppointment;

    if(beforeAppointments.length>0){
      beforeAppointment=beforeAppointments[0];
      if(beforeAppointment._doc.doctor) beforeAppointment._doc.doctor = restructureProfileObj(beforeAppointment.doctor , false)

    }
    if(afterAppointments.length>0){
      afterAppointment=afterAppointments[0];
      if(afterAppointment._doc.doctor) afterAppointment._doc.doctor = restructureProfileObj(afterAppointment.doctor , false)
    }

    if(beforeAppointment && beforeAppointment.endTime){
      if(new Date(beforeAppointment.endTime).getTime()<=new Date(time).getTime()) beforeAppointment=null;
    }

    if(afterAppointment && afterAppointment.startTime){
      if(new Date(endTime).getTime()<new Date(afterAppointment.startTime).getTime()) afterAppointment=null;
    }

    let isOk=true,dayFull=false;
    if(afterAppointment || beforeAppointment) isOk=false;
    if(beforeAppointment) conflits.push(beforeAppointment);
    if(afterAppointment && (!beforeAppointment || (beforeAppointment._id && beforeAppointment._id+""!==afterAppointment._id+""))) conflits.push(afterAppointment);

    return {isOk,conflits,beforeAppointment,afterAppointments}
  }

  async mixAppointmentsTimeoffs(filters,user){
    let queryAppointment={
      hospital:mongoose.Types.ObjectId(user.profile.hospital._id),
      date:new Date(filters.date),
      state:{$in:["APPROVED", "INPROGRESS"]}
    };
    if(filters.doctor){
      const doctor = await Profile.findOne({_id: filters.doctor})
      queryAppointment.doctor=mongoose.Types.ObjectId(doctor.staff._id);
    } 
    let appointments=await this.model.find(queryAppointment).select('startTime type supply doctor');
    appointments=appointments.map(x=>{
      x._doc.endTime=new Date(new Date(x.startTime).getTime() + this.estimateAppointmentDuration(x,user)*60000);
      return x;
    });
    let queryTimeoffs={
      hospital:mongoose.Types.ObjectId(user.profile.hospital._id),date:new Date(filters.date)};
    let timeoffs=await this.addFixTimeoffs(queryTimeoffs,user);
    if(timeoffs)timeoffs=timeoffs.timeoffs;

    appointments=appointments.concat(timeoffs.filter(x=>x.isActive && !x.deleted));
    appointments=appointments.sort((a,b)=>new Date(a.startTime).getTime()-new Date(b.startTime).getTime());
    return appointments;
  }

  estimateAppointmentDuration(appointment,user){
    let duration=0;
    if(!appointment.supply)
        if( appointment.supply && appointment.supply._id){
          duration=user.profile.hospital.sessions.find(x=>x._id==(appointment.supply._id ||appointment.supply) );
          if(duration)duration=duration.avgDuration;
          else duration=0;
        }
        if(!duration) {
          user.profile.hospital.sessions.map(x=>{
            if(x.avgSessionDuration && x.avgSessionDuration>0)
            duration=duration+x.avgSessionDuration;
          });
          if(user.profile.hospital.sessions.length>0) duration=duration/(user.profile.hospital.sessions.filter(x=>x.avgSessionDuration>0).length);
        }
        if(!duration) duration=user.profile.hospital.avgSessionDuration || 20;
      return duration;
  }
  async checkBeforeTimeoff(startTime,endTime,doctor,user){
    let query={
      hospital:mongoose.Types.ObjectId(user.profile.hospital._id),
      startTime:{$gte:new Date(startTime),$lte:new Date(endTime)},
      doctor:mongoose.Types.ObjectId(doctor),
  };
  let appointments=await this.model.find(query);
  if(!appointments) throw new APIError(404, 'appointments cannot fid');

  return {appointments:appointments.length}
  }
  getContentOfTimeoff(timeoffBefore,timeoff,operation,lang){
    let content="";
    if(operation=="create"){
      if(timeoff.isWeekly) content=this.translate(lang," a programmé une nouvelle indisponibilité chaque ")+getDaysOfWeek(timeoff.day)+this.translate(lang," de ")+moment(timeoff.startTime).format("HH:mm")+this.translate(lang," à ")+moment(timeoff.endTime).format("HH:mm")
      if(!timeoff.isWeekly) content=this.translate(lang," a programmé une nouvelle indisponibilité le ")+moment(timeoff.date).format('YYYY-MM-DD')+" de "+moment(timeoff.startTime).format("HH:mm")+this.translate(lang," à ")+moment(timeoff.endTime).format("HH:mm")
    }
    if(operation=="update"){
      if( !timeoffBefore  || (timeoffBefore && timeoffBefore.state==timeoff.state)){
         if(timeoff.isWeekly) content=this.translate(lang," a modifié une indisponibilité hebdomadaire du ")+getDaysOfWeek(timeoff.day)+this.translate(lang," de ")+moment(timeoff.startTime).format("HH:mm")+this.translate(lang," à ")+moment(timeoff.endTime).format("HH:mm")
      if(!timeoff.isWeekly) content=this.translate(lang," a modifié une indisponibilité le ")+moment(timeoff.date).format('YYYY-MM-DD')+this.translate(lang," de ")+moment(timeoff.startTime).format("HH:mm")+this.translate(lang," à ")+moment(timeoff.endTime).format("HH:mm")
      }else {
        if(timeoff.isActive) {
          if(timeoff.isWeekly) content=this.translate(lang," a activé une indisponibilité hebdomadaire du ")+getDaysOfWeek(timeoff.day)+this.translate(lang," de ")+moment(timeoff.startTime).format("HH:mm")+this.translate(lang," à ")+moment(timeoff.endTime).format("HH:mm")
          if(!timeoff.isWeekly) content=this.translate(lang," a activé une indisponibilité le ")+moment(timeoff.date).format('YYYY-MM-DD')+" de "+moment(timeoff.startTime).format("HH:mm")+this.translate(lang," à ")+moment(timeoff.endTime).format("HH:mm")
        }
        else{
          if(timeoff.isWeekly) content=this.translate(lang," a désactivé une indisponibilité hebdomadaire du ")+getDaysOfWeek(timeoff.day)+this.translate(lang," de ")+moment(timeoff.startTime).format("HH:mm")+this.translate(lang," à ")+moment(timeoff.endTime).format("HH:mm")
          if(!timeoff.isWeekly) content=this.translate(lang," a désactivé une indisponibilité le ")+moment(timeoff.date).format('YYYY-MM-DD')+this.translate(lang," de ")+moment(timeoff.startTime).format("HH:mm")+this.translate(lang," à ")+moment(timeoff.endTime).format("HH:mm")
        }
      }
     
    }
    if(operation=="delete"){
      if(timeoff.isWeekly) content=this.translate(lang," a supprimé une indisponibilité hebdomadaire du ")+getDaysOfWeek(timeoff.day)+this.translate(lang," de ")+moment(timeoff.startTime).format("HH:mm")+this.translate(lang," à ")+moment(timeoff.endTime).format("HH:mm")
      if(!timeoff.isWeekly) content=this.translate(lang," a supprimé une indisponibilité du ")+moment(timeoff.date).format('YYYY-MM-DD')+this.translate(lang," de ")+moment(timeoff.startTime).format("HH:mm")+this.translate(lang," à ")+moment(timeoff.endTime).format("HH:mm")
    }
    return content;
  }
  translate(lang,string){
    switch(string){
      case ' a supprimé une indisponibilité hebdomadaire du ':{
        if(lang=="fr") return string;
        if(lang=="ar") return ' حذف غياب أسبوعي كل  ';
        if(lang=="en") return ' has deleted a weekly unavailability every ';
      }
      case ' a supprimé une indisponibilité du ':{
        if(lang=="fr") return string;
        if(lang=="ar") return ' حذف غياب  ';
        if(lang=="en") return ' has deleted an unavailability ';
      }
      case ' de ':{
        if(lang=="fr") return string;
        if(lang=="ar") return ' من ';
        if(lang=="en") return ' from ';
      }
      case ' à ':{
        if(lang=="fr") return string;
        if(lang=="ar") return ' الى ';
        if(lang=="en") return ' to ';
      }
      case ' a activé une indisponibilité hebdomadaire du ':{
        if(lang=="fr") return string;
        if(lang=="ar") return ' فعل   غياب أسبوعي كل  ';
        if(lang=="en") return ' has activated a weekly unavailability every  ';
      }
      case ' a activé une indisponibilité le ':{
        if(lang=="fr") return string;
        if(lang=="ar") return ' فعل غياب  ';
        if(lang=="en") return ' has activated an unavailability  ';
      }
      case ' a désactivé une indisponibilité hebdomadaire du ':{
        if(lang=="fr") return string;
        if(lang=="ar") return ' عطل   غياب أسبوعي كل  ';
        if(lang=="en") return ' has deactivated a weekly unavailability every ';
      }
      case ' a désactivé une indisponibilité le ':{
        if(lang=="fr") return string;
        if(lang=="ar") return ' عطل غياب  ';
        if(lang=="en") return ' has deactivated a weekly unavailability ';
      }
      case ' a programmé une nouvelle indisponibilité chaque ':{
        if(lang=="fr") return string;
        if(lang=="ar") return ' برنمج غياب أسبوعي جديد';
        if(lang=="en") return ' has programmed a new weekly unavailability every ';
      }
      case ' a programmé une nouvelle indisponibilité le ':{
        if(lang=="fr") return string;
        if(lang=="ar") return ' برنمج غياب جديد';
        if(lang=="en") return ' has programmed a new unavailability  ';
      }
      case ' a modifié une indisponibilité hebdomadaire du ':{
        if(lang=="fr") return string;
        if(lang=="ar") return ' عدل غياب أسبوعي';
        if(lang=="en") return ' has changed an unavailability of ';
      }
      case ' a modifié une indisponibilité le ':{
        if(lang=="fr") return string;
        if(lang=="ar") return ' عدل غياب ';
        if(lang=="en") return '  has changed an unavailability ';
      }
      default:{
        return string
      }
    }
  }
   validateEmail(email) {
    const re = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return re.test(String(email).toLowerCase());
}
  async sendToPatient(appointment,hostname,user){
    let patient=appointment.patient; 
    if(patient._id)patient=patient._id;
    let hospital=appointment.hospital; 
    if(hospital._id)hospital=hospital._id;
    if(appointment.patient.email && this.validateEmail(appointment.patient.email)){
        let oldAppointment=await this.model.findOne({
      hospital:mongoose.Types.ObjectId(hospital),
      patient:mongoose.Types.ObjectId(patient),
      state:{$nin:['CANCELED']},
      _id:{$nin:[appointment._id]}
    });
    let msgForAppointment="Bonjour "+ this.getName(appointment.patient.firstName)
    +",<br>Vous avez un rendez vous avec Dr "+this.getName(appointment.doctor.firstName)+" "+this.getName(appointment.doctor.lastName)+" le :"+moment(appointment.date).format('YYYY-MM-DD')+" à "+moment(appointment.date).format('HH:mm')+"<br>"
      let link;
      if(!oldAppointment){
        link=await this.FirstAppointmentForPatient(appointment,hostname); 
        msgForAppointment=msgForAppointment+"Pour modifier les informations de votre profile :";
      }
    sendEmail(appointment.patient.email,'Rendez-vous : Dr '+this.getName(appointment.doctor.firstName)+" "+this.getName(appointment.doctor.lastName)+" ",msgForAppointment,link,user.profile.hospital)
  }
  
  }
  getName(string){
    return string.trim()[0].toUpperCase() + string.slice(1).toLowerCase();
  }
  async FirstAppointmentForPatient(appointment,hostname){
    let objectToToken={patient:appointment.patient._id,hospital:appointment.hospital,appointment:appointment._id};
    let hoursBeforeAppointment=((new Date(appointment.startTime).getTime()-new Date().getTime()))/(3600*1000);
    // Add expiration that was commented out
    let token=jwt.sign(objectToToken, process.env.JWT_SECRET_KEY || config.JWT_SECRET_KEY, {
      expiresIn: Math.max(1, hoursBeforeAppointment) + 'h' // At least 1 hour, or more based on appointment time
    });
    token=hostname+'/patient/'+token;
    return token;
  }
  async doctorBadgeStat(user){
    let query={
      hospital:mongoose.Types.ObjectId(user.profile.hospital._id),
      date:getUtcDate(Date.now()),
      state:{$in:['APPROVED','INPROGRESS']}
    }
    if(user.profile.title=="DOCTOR"){
      query.doctor=mongoose.Types.ObjectId(user.profile._id);
    }
    else {
      query.doctor={$in:user.profile.doctors};
    }
    let appointments=await this.model.find(query).select('patientArrived state');
    let result={
      patientAtOffice:appointments.filter(x=>x.state=="APPROVED" && x.patientArrived).length,
      inProgressAppointments:appointments.filter(x=>x.state=="INPROGRESS" ).length,
    };
    return result;
  }
  async updatePatientSessionsCount(patientID,user){
    let sessions=await Session.find({hospital:mongoose.Types.ObjectId(user.profile.hospital._id),patient:mongoose.Types.ObjectId(patientID)});
    sessions = sessions.filter(s => s.appointment.state === "COMPLETED");
    let count=sessions.length
    let p=await Patient.findOneAndUpdate({hospital:mongoose.Types.ObjectId(user.profile.hospital._id),_id:mongoose.Types.ObjectId(patientID)},
     {
      sessionCounts:count
     },{new:true}
    
    )
  }
}

export default AppointmentService;