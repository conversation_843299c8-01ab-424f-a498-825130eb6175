import Controller from './Controller';
import SessionService from "../services/SessionService";
import Session from "../models/Session";
const sessionService = new SessionService(Session);

class SessionController extends Controller {

  constructor(service) {
    super(service);
    this.getSessions = this.getSessions.bind(this);
    this.createSession = this.createSession.bind(this);
    this.updateSession = this.updateSession.bind(this);
    this.detailSession = this.detailSession.bind(this);
    this.createOrEditNote=this.createOrEditNote.bind(this);
    this.deleteNote=this.deleteNote.bind(this);
    this.createOrEditDrug=this.createOrEditDrug.bind(this);
    this.deleteDrug=this.deleteDrug.bind(this);
    this.addDiagnose=this.addDiagnose.bind(this);
    this.deleteDiagnose=this.deleteDiagnose.bind(this);
    this.toAlmostCompleted=this.toAlmostCompleted.bind(this);
    this.averageTime=this.averageTime.bind(this);
    this.createOrEditPrescriptionPage=this.createOrEditPrescriptionPage.bind(this);
    this.deletePrescriptionPage=this.deletePrescriptionPage.bind(this);

  }

  async getSessions(req) {
    return sessionService.getSessions(req.body, req.body.page, req.body.limit, req.user);
  }
  async createSession(req) {
    return sessionService.createSession(req.body.session, req.body.appointment = {}, req.user);
  }
  async updateSession(req) {
    return sessionService.updateSession(req.body.sessionID, req.body.session, req.user);
  }
  async detailSession(req) {
    return sessionService.detailSession(req.body, req.user);
  }
  async createOrEditNote (req) {
    return sessionService.createOrEditNote(req.body.note,req.body.sessionID,req.user,req.body.socketID);
  }
  async deleteNote (req) {
    return sessionService.deleteNote(req.body.noteID,req.body.sessionID,req.user,req.body.socketID);
  }
  async createOrEditDrug (req) {
    return sessionService.createOrEditDrug(req.body.drug,req.body.sessionID,req.user,req.body.socketID);
  }
  async deleteDrug (req) {
    return sessionService.deleteDrug(req.body.drugID,req.body.sessionID,req.user,req.body.socketID);
  }
  async addDiagnose (req) {
    return sessionService.addDiagnose(req.body.diagnoseID,req.body.sessionID,req.user,req.body.socketID);
  }
  async deleteDiagnose (req) {
    return sessionService.deleteDiagnose(req.body.diagnoseID,req.body.sessionID,req.user,req.body.socketID);
  }
  async toAlmostCompleted (req) {
    return sessionService.toAlmostCompleted(req.body.appointmentID,req.user,req.body.socketID);
  }
  async averageTime (req) {
    return sessionService.averageTime(req.body,req.user);
  }
  async createOrEditPrescriptionPage (req) {
    return sessionService.createOrEditPrescriptionPage(req.body.sessionID,req.body.type,req.body.page,req.user,req.body.socketID);
  }
  async deletePrescriptionPage (req) {
    return sessionService.deletePrescriptionPage(req.body.sessionID,req.body.prescriptionPageId,req.user,req.body.socketID);
  }
}

export default new SessionController(sessionService);