import bcrypt from 'bcryptjs';
import config from '../../config/config';
const mongoose = require('mongoose');

const { Schema } = mongoose;
const UserSchema = new Schema({
  email: {
    type: String,
    unique: true,
    required: false, // Email is auto-generated if not provided
    lowercase: true
  },
  password: {
    type: String,
    default: ''
  },
  levelOfAccess: {
    type: Number,
    min: 0,
    max: 5,
    required: true,
    default: 4
  },
  active: {
    type: Boolean,
    default: false
  },
  alreadyConnected: {
    type: Boolean,
    default: false
  },
  profile: {
    type: mongoose.Types.ObjectId,
    ref: 'Profile',
    default: null
  },
  deletedAt: {
    type: Date,
    default: null
  },
  lastTimeConnected: {
    type: Date,
    default: null
  },
  totalConnections: {
    type: Number,
    default: 0
  },
  FCMRegToken: {
    type: [String],
    default: null
  },
  expiresIn: {
    type: Number,
    default: process.env.EXPIRESIN || config.EXPIRESIN || 86400 // 24 hours in seconds
  }
}, {
  timestamps: true,
  collation: { locale: 'fr', strength: 1 }
});
// crypt when edit the profile password
UserSchema.pre('save', function (next) {
  if (!this.isModified('password') && !this.isNew || !this.password) return next();
  bcrypt.hash(this.password, 10).then((hash, err) => {
    this.password = hash;
    return next();
  }).catch(err => {
    return next(err);
  });
});

UserSchema.pre('find', softDeleteMiddleware);
UserSchema.pre('findOne', softDeleteMiddleware);

UserSchema.methods.toJSON = function () {
  var obj = this.toObject();
  delete obj.password;
  return obj;
};

UserSchema.methods.comparePassword = async function (password) {
  return bcrypt.compare(password, this.password);
};
function softDeleteMiddleware(next) {
  // If `isDeleted` is not set on the query, set it to `false` so we only
  // get docs that haven't been deleted by default
  const filter = this.getQuery();
  filter.deletedAt = null;
  next();
}

module.exports = mongoose.model('User', UserSchema);