const nodemailer = require("nodemailer")
// const mg = require("nodemailer-mailgun-transport")
const handlebars = require("handlebars")
const fs = require("fs")
const path = require("path")
const emailTemplateSource = fs.readFileSync(path.join(__dirname, "./template-link.hbs"), "utf8")


export const sendEmail = (toEmail,title,body,link,hospital,image) => {
  require('dotenv').config({ path: process.env.NODE_ENV ? `environments/.env.${process.env.NODE_ENV}` : "environments/.env.dev" });

    // const mailgunAuth = {
    //     auth: {
    //       api_key: process.env.MAILGUN_APIKEY,
    //       domain: process.env.MAIL_HOST,
    //         },
    //         host:"api.eu.mailgun.net",
    //
    //     };
    let transporter = nodemailer.createTransport({
        host: process.env.MAIL_HOST,
        port: 25,
        secureConnection: false,
        auth: {
            user: process.env.EMAIL,
            pass: process.env.PASSWORD
        },
        tls: {
            ciphers:'SSLv3',
            rejectUnauthorized: false
        }
    })
    // transporter.verify((err, success) => {
    //     if (err) console.error(err);
    //     console.log('Your config is correct');
    // });
    // const message = {
    //     from: "<EMAIL>",
    //     to: "<EMAIL>",
    //     subject: "Subject",
    //     text: "Hello SMTP Email"
    // }
    //   const smtpTransport = nodemailer.createTransport(transporter)

      handlebars.registerHelper('checkNull', function(value) {
        return (!value ||value =='') ? '' : value;
    });
      const template = handlebars.compile(emailTemplateSource);
      if(!image)
       image="https://lbetvg.stripocdn.email/content/guids/CABINET_bda12376a099b6ed7b496b9f63e10a1d/images/70961616222902676.png";
      const htmlToSend = template({message: body,title,link,hospital,image})

      const mailOptions = {
        from: `WinMed <<EMAIL>>`,
        to: toEmail,
        subject: title,
        html: htmlToSend
      }

    transporter.sendMail(mailOptions, function(error, response) {
        if (error) {
          console.log(error);
        } else {
          console.log("Successfully sent email.");
        }
      })
};
