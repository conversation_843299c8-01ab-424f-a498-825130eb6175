import mongoose from "mongoose";
import { ROOMS_SCHEDULES, PRACTICES_TYPES } from "../../config/utils/variables";

const Schema = mongoose.Schema;

const RoomSchema = new mongoose.Schema({
    hospital: {
        type: mongoose.Types.ObjectId,
        ref: 'Hospital',
        required: true
    },
    roomNumber: {
        type: String,
        default: null,
        required: true,
    },
    standards: {
        type: String
    },
    type: {
        type: String,
        enum: PRACTICES_TYPES,
        uppercase: true

    },
    createdBy: {
        type: mongoose.Types.ObjectId,
        ref: 'Profile'
    },
    updatedBy: {
        type: mongoose.Types.ObjectId,
        ref: 'Profile'
    }
}, {
    timestamps: true,
    collation: { locale: 'fr', strength: 1 }
});

RoomSchema.pre('find', sortMiddlware);
function sortMiddlware(next) {
    this.sort({ roomNumber: 1 });
    next();
}

module.exports = mongoose.model("Room", RoomSchema);