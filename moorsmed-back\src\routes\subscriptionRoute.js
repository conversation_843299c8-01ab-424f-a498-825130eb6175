import SubscriptionController from '../controllers/SubscriptionController';
import { IS_MANAGER_HOSPITAL } from '../middlewares/authenticators';

export default (createEndpoint => {
  createEndpoint({
    method: 'post',
    path: '/getPacks',
    verify: [IS_MANAGER_HOSPITAL],
    controller: SubscriptionController.getPacks
  });
  createEndpoint({
    method: 'post',
    path: '/populatePacks',
    verify: [IS_MANAGER_HOSPITAL],
    controller: SubscriptionController.populatePacks
  });
});