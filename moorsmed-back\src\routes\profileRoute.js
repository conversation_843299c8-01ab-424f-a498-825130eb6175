import ProfileController from '../controllers/ProfileController';
import { IS_LOGGED_IN, IS_ALLOWED } from '../middlewares/authenticators';

export default (createEndpoint => {
  createEndpoint({
    method: 'post',
    path: '/create',
    verify: [IS_LOGGED_IN, IS_ALLOWED(2)],
    controller: ProfileController.addProfile
  });
  createEndpoint({
    method: 'post',
    path: '/find',
    verify: [IS_LOGGED_IN],
    controller: ProfileController.findProfiles
  });
  createEndpoint({
    method: 'post',
    path: '/findOne',
    verify: [IS_LOGGED_IN],
    controller: ProfileController.findOneProfile
  });
  createEndpoint({
    method: 'post',
    path: '/edit',
    verify: [IS_LOGGED_IN],
    controller: ProfileController.editProfile
  });

  createEndpoint({
    method: 'post',
    path: '/delete',
    verify: [IS_LOGGED_IN, IS_ALLOWED(4)],
    controller: ProfileController.deleteProfiles
  });
  createEndpoint({
    method: 'post',
    path: '/restore',
    verify: [IS_LOGGED_IN, IS_ALLOWED(4)],
    controller: ProfileController.restoreProfiles
  });
  createEndpoint({
    method: 'get',
    path: '/getProfilePic/:profileID',
    controller: ProfileController.getProfilePic
  });
  createEndpoint({
    method: 'post',
    path: '/getPatients',
    verify: [IS_LOGGED_IN],
    controller: ProfileController.getPatients
  });
  createEndpoint({
    method: 'post',
    path: '/patientEditPatient',
    controller: ProfileController.patientEditPatient
  });
  createEndpoint({
    method: 'post',
    path: '/patientGetPatient',
    controller: ProfileController.patientGetPatient
  });
  createEndpoint({
    method: 'post',
    path: '/editLang',
    verify: [IS_LOGGED_IN],
    controller: ProfileController.editLang
  });
});
