import mongoose from "mongoose";
import mongoosePaginate from 'mongoose-paginate';

const Schema = mongoose.Schema;

const PatientSchema = new mongoose.Schema({
    hospital: {
        type: Schema.Types.ObjectId,
        ref: 'Hospital'
    },
    deletedAt: {
        type: Date,
        default: null
    },
    updatedBy: {
        type: Schema.Types.ObjectId,
        ref: 'Profile'
    },
    createdBy: {
        type: Schema.Types.ObjectId,
        ref: 'Profile'
    },
    allergies :[String],
    chronicDiseases:[String],
    permanentDrugs:[String],
    insurance:{
        type: String,
    },
    insuranceId:{
        type: String,
    },
    sessionCounts:{
        type: Number,
        default:0
    },
    profile: {
        type: Schema.Types.ObjectId,
        ref: 'Profile'
    },
}, {
    timestamps: true,
    collation: { locale: 'fr', strength: 1 }
});

PatientSchema.pre('find', softDeleteMiddleware);
PatientSchema.pre('findOne', softDeleteMiddleware);
PatientSchema.pre('sort', softDeleteMiddleware);
PatientSchema.plugin(mongoosePaginate);

PatientSchema.statics.restore = function (query, callback) {
    return this.updateMany(query, { $set: { deletedAt: null } }, callback);
};

PatientSchema.statics.softDelete = function (query, callback) {
    return this.findOneAndUpdate(query, { $set: { deletedAt: Date.now() } }, { new: true }, callback);
};

PatientSchema.statics.softDeleteMany = function (query, callback) {
    return this.updateMany(query, { $set: { deletedAt: Date.now() } }, { new: true }, callback);
};

function softDeleteMiddleware(next) {
    var filter = this.getQuery();
    filter.deletedAt = null;
    next();
}
module.exports = mongoose.model('Patient', PatientSchema);
