import InvoiceController from '../controllers/InvoiceController';
import { IS_LOGGED_IN } from '../middlewares/authenticators';

export default (createEndpoint => {
  createEndpoint({
    method: 'post',
    path: '/getInvoices',
    verify: [IS_LOGGED_IN],
    controller: InvoiceController.getInvoices
  });
  createEndpoint({
    method: 'post',
    path: '/getInvoice',
    verify: [IS_LOGGED_IN],
    controller: InvoiceController.getInvoice
  });
  createEndpoint({
    method: 'post',
    path: '/createOrUpdateInvoice',
    verify: [IS_LOGGED_IN],
    controller: InvoiceController.createOrUpdateInvoice
  });
  createEndpoint({
    method: 'post',
    path: '/addUpdateItem',
    verify: [IS_LOGGED_IN],
    controller: InvoiceController.addUpdateItem
  });
  createEndpoint({
    method: 'post',
    path: '/deleteItem',
    verify: [IS_LOGGED_IN],
    controller: InvoiceController.deleteItem
  });
});