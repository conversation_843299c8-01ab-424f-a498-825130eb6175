require('dotenv').config({ path: process.env.NODE_ENV ? `environments/.env.${process.env.NODE_ENV}` : "environments/.env.dev" });
import Hospital from './../src/models/Hospital';

export default async function Server(server) {
  let corsConfig = {
    origin: '*',
  };
  if(process.env.NODE_ENV == 'production') {
    corsConfig = {
      origin:  process.env.allowedOrigins ? process.env.allowedOrigins.split(',')[0] : [],
    };
  }
  var io = require('socket.io')(server, {
    cors: corsConfig
  });
  global.io = io.of('/office');
  global.connectedSockets = {};
  let hospitals = await Hospital.find().distinct('_id');
  let sockets = {};
  global.io.on('connection', (socket) => {
    let hospital = socket.handshake.query.hospital;
    let profile = socket.handshake.query.profile;

    if (hospitals.some((w) => w + '' == hospital + '')) {
      if (!global.connectedSockets[hospital + ''])
        global.connectedSockets[hospital + ''] = {};
      if (!global.connectedSockets[hospital + ''][profile + '']) {
        global.connectedSockets[hospital + ''][profile + ''] = [socket];
      } else {
        global.connectedSockets[hospital + ''][profile + ''].push(socket);
      }
      socket.join(hospital);
    } else {
      console.log('random person without hospital trying to connect');
    }

    socket.on('disconnect', (data) => {
      Object.keys(global.connectedSockets).map((hospital) => {
        Object.keys(global.connectedSockets[hospital]).map((profile) => {
          global.connectedSockets[hospital][profile] = global.connectedSockets[hospital][
            profile
          ].filter((x) => x !== socket);
        });
      });
      socket.disconnect();
    });
    socket.on('logout', (data) => {
      if (data.profile && data.hospital)
        delete global.connectedSockets[data.hospital][data.profile];
    });
  });

  return this;
}
