import mongoose from "mongoose";
import { APPOINTMENT_TYPES, APPOINTMENT_STATES } from "../../config/utils/variables";
import { getUtcDate, copyTime } from "../helpers/dates";
import mongoosePaginate from 'mongoose-paginate';

const Schema = mongoose.Schema;

const AppointmentSchema = new mongoose.Schema({
    hospital: {
        type: Schema.Types.ObjectId,
        ref: 'Hospital'
    },
    patient: {
        type: Schema.Types.ObjectId,
        ref: 'Patient'
    },
    doctor: {
        type: Schema.Types.ObjectId,
        ref: 'Staff'
    },
    description: {
        type: String
    },
    type: {
        type: String
        },
    date: {
        type: Date,
        default: Date.now()
    },
    startTime: {
        type: Date,
        default: Date.now()
    },
    endTime: {
        type: Date,
        default: Date.now()
    },
    patientArrived: {
        type: Date
    },
    state: {
        type: String,
        enum: APPOINTMENT_STATES,
        uppercase: true,
        default: "PENDING"
    },
    updatedBy: {
        type: Schema.Types.ObjectId,
        ref: 'Profile'
    },
    createdBy: {
        type: Schema.Types.ObjectId,
        ref: 'Profile'
    },
    fromSession: {
        type: Schema.Types.ObjectId,
        ref: 'Session'
    },
    supply: {
        type: Schema.Types.ObjectId,
        ref: 'Supply'
    },
    waitingTime:{type:Number},
    docs: [{ title: String, link: String }],
    files: [{ title: String, link: String }],
    stateOrder:{type:Number},
    certificate: {
        date: Date,
        fromDate: Date,
        toDate: Date,
        releasedFrom:String,
        reason:String
    },


}, {
    timestamps: true,
    collation: { locale: 'fr', strength: 1 }
});
AppointmentSchema.pre('validate', function (next) {
    this.date = getUtcDate(this.date);
    if (this.startTime && this.endTime) {
        this.startTime = copyTime(this.date, this.startTime);
        this.endTime = copyTime(this.date, this.endTime);
    }
    next();
});
AppointmentSchema.pre('save', function (next) {
    let doc=this;
    this.stateOrder=defineStateOrder(doc).stateOrder;
    next();
});

AppointmentSchema.plugin(mongoosePaginate);
AppointmentSchema.pre('find', populateAppointments);
AppointmentSchema.pre('findOne', populateAppointments);
AppointmentSchema.pre('findOneAndUpdate', populateAppointments);
function populateAppointments(next) {
    if(this.getUpdate()){
        let doc = this.getUpdate();
        doc=defineStateOrder(doc);
        
    next();
    }
    this
    .populate({
        path : 'supply',
        select : 'name sellingPrice avgDuration'
    })
    .populate({
        path : 'patient',
        populate : [{path : "profile" , select: "-staff -patient -supplier -superAdmin"}]
    })
    .populate({
        path : 'doctor',
        populate : [{path : "profile", select: "-staff -patient -supplier -superAdmin"}]
    })
    .populate('createdBy', "firstName lastName title -staff -patient -supplier -superAdmin")
    .populate('updatedBy', "firstName lastName title -staff -patient -supplier -superAdmin")
    .populate('fromSession', "date name")
    .sort({ date: 1, createdBy: 1, updatedBy: 1, state: 1, type: 1 });


    next();
}
function defineStateOrder(doc){
    if(doc.state=="ALMOST_COMPLETED") doc.stateOrder=2;
    else if(doc.state=="INPROGRESS") doc.stateOrder=1;
    else doc.stateOrder=0;
    return doc
}

module.exports = mongoose.model("Appointment", AppointmentSchema);