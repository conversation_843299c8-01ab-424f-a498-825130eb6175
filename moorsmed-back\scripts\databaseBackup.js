require('dotenv').config({ path: process.env.NODE_ENV ? `environments/.env.${process.env.NODE_ENV}` : "environments/.env.dev" });

const moment = require('moment');
const { exec } = require('child_process');
const path = require('path');

// Create the backup directory if it doesn't exist
const fs = require('fs');
if (!fs.existsSync(process.env.DB_BACKUP_PATH)) {
  fs.mkdirSync(process.env.DB_BACKUP_PATH);
}

const backupDate = moment().format('YYYY-MM-DD');
const backupDirectory = path.join(process.env.DB_BACKUP_PATH, backupDate);

// Execute mongodump command
const command = `mongodump --db ${process.env.DATABASE_NAME} --out ${backupDirectory} `;

exec(command, (error, stdout, stderr) => {
  if (error) {
    console.error('Error during backup:', error.message);
    return;
  }
  if (stderr) {
    console.error('Error during backup:', stderr);
    return;
  }
  console.log('Backup successful!');
});
