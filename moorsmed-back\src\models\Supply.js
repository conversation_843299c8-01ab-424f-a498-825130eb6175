import mongoose from "mongoose";
import { SUPPLIES_TYPES } from "../../config/utils/variables";
import mongoosePaginate from 'mongoose-paginate';

const Schema = mongoose.Schema;

const SupplySchema = new mongoose.Schema({
    hospital: {
        type: Schema.Types.ObjectId,
        ref: 'Hospital'
    },
    name: {
        type: String,
        required: true
    },
    description: {
        type: String,
    },
    quantity: {
        type: Number,
        default:0
    },
    consumedNumber: {
        type: Number,
        default:0
    },
    costPrice: {
        type: Number,
    },
    sellingPrice: {
        type: Number,
        default:0,
    },
    avgDuration: {
        type: Number,
        default:0,
    },
    type: {
        type: String,
        enum: SUPPLIES_TYPES,
        uppercase: true
    },
    updatedBy: {
        type: Schema.Types.ObjectId,
        ref: 'Profile'
    },
    createdBy: {
        type: Schema.Types.ObjectId,
        ref: 'Profile'
    },
    supplier: {
        type: Schema.Types.ObjectId,
        ref: 'Supplier'
    },
    otherSuppliers: [{
        type: Schema.Types.ObjectId,
        ref: 'Supplier'
    }],
    deletedAt: {
        type: Date,
        default: null
    },
}, {
    timestamps: true,
    collation: { locale: 'fr', strength: 1 }
});
SupplySchema.plugin(mongoosePaginate);
SupplySchema.pre('find', populateSupplies);
SupplySchema.pre('findOne', populateSupplies);
SupplySchema.pre('findOneAndUpdate', populateSupplies);
SupplySchema.statics.softDeleteMany = function (query, callback) {
    return this.updateMany(query, { $set: { deletedAt: Date.now() } }, { new: true }, callback);
};
function populateSupplies(next) {
    this.populate('createdBy', "firstName lastName title")
    .populate('updatedBy', "firstName lastName title")
    .populate({path : 'supplier' , populate: {path : "profile" , select: "firstName lastName title phoneNumber adress profilePic -staff -patient -supplier -superAdmin"}})
    .populate({path : 'otherSuppliers' , populate: {path : "profile" , select: "firstName lastName title phoneNumber adress profilePic -staff -patient -supplier -superAdmin"}})

    .sort({type:-1 ,name: 1, createdBy: 1, updatedBy: 1 });

    next();
}
module.exports = mongoose.model("Supply", SupplySchema);