import ReportingController from '../controllers/ReportingController';
import { IS_LOGGED_IN } from '../middlewares/authenticators';

export default (createEndpoint => {
  createEndpoint({
    method: 'post',
    path: '/sessionTypesPie',
    verify: [IS_LOGGED_IN],
    controller: ReportingController.sessionTypesPie
  });
  createEndpoint({
    method: 'post',
    path: '/sessionTypesLines',
    verify: [IS_LOGGED_IN],
    controller: ReportingController.sessionTypesLines
  });
  createEndpoint({
    method: 'post',
    path: '/waitingTimeBars',
    verify: [IS_LOGGED_IN],
    controller: ReportingController.waitingTimeBars
  });
  createEndpoint({
    method: 'post',
    path: '/dayOfWeeksSessions',
    verify: [IS_LOGGED_IN],
    controller: ReportingController.dayOfWeeksSessions
  });
  createEndpoint({
    method: 'post',
    path: '/workedHours',
    verify: [IS_LOGGED_IN],
    controller: ReportingController.workedHours
  });
  createEndpoint({
    method: 'post',
    path: '/ratesData',
    verify: [IS_LOGGED_IN],
    controller: ReportingController.ratesData
  });
  
});