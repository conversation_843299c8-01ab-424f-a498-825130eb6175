import Controller from './Controller';
import SubscriptionService from "../services/SubscriptionService";
import TenantSubscription from "../models/TenantSubscription";
const subscriptionService = new SubscriptionService(TenantSubscription);

class SubscriptionController extends Controller {

  constructor(service) {
    super(service);
    this.getPacks = this.getPacks.bind(this);
    this.populatePacks = this.populatePacks.bind(this);
  }

  async getPacks(req) {
    return subscriptionService.getPacks(req.body , req.user);
  }

  async populatePacks(){
    return subscriptionService.populatePacks();
  }

}

export default new SubscriptionController(subscriptionService);