import mongoose from "mongoose";
const Schema = mongoose.Schema;
import mongoosePaginate from 'mongoose-paginate';
import {SUBSCRIPTION_PERIODICITY} from "../../config/utils/variables";

const TenantSubscriptionSchema = new mongoose.Schema({
    tenant: {
        type: Schema.Types.ObjectId,
        ref: 'Tenant'
    },
    periodicity: {
        type: String,
        enum: SUBSCRIPTION_PERIODICITY
    },
    actif: {
        type: Boolean,
    },
    terminated: {
        type: Boolean,
    },
    startDate: {
        type: Date,
    },
    endDate: {
        type: Date,
    },
    numberOfUsers: {
        type: Number
    },
    comment: {
        type: String
    },
    packs: [{
        type: Schema.Types.ObjectId,
        ref: 'Pack'
    }],
    deletedAt: {
        type: Date,
        default: null
    },
}, {
    timestamps: true,
    collation: { locale: 'fr', strength: 1 }
});
TenantSubscriptionSchema.plugin(mongoosePaginate);
TenantSubscriptionSchema.pre('find', populateTenantSubscription);
TenantSubscriptionSchema.pre('findOne', populateTenantSubscription);
TenantSubscriptionSchema.pre('findOneAndUpdate', populateTenantSubscription);
function populateTenantSubscription(next) {
    this.populate('packs');
    next();
}
module.exports = mongoose.model("TenantSubscription", TenantSubscriptionSchema);