export const groupBy = (data, keys) => {
    let results = [];
    data.map(x => {
        let alreadyThereIndex = results.findIndex(r => {
            return keys.map(k => r[k] == x[k]).every(e => e === true);
        });
        if (alreadyThereIndex < 0) {
            let newItem = { data: [x] };
            keys.map(k => {
                newItem[k] = x[k];
            });
            results.push(newItem);
        } else {
            results[alreadyThereIndex].data.push(x);
        }
    });
    return results;
};

export const paginate = (array, limit, page) => {

    const total = array.length;
    const pages = Math.ceil(total / limit);
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedArray = array.slice(startIndex, endIndex);
  
    return {
      docs: paginatedArray,
      limit: limit,
      page: page,
      pages: pages,
      total: total
    };
    
}