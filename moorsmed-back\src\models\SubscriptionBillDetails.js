import mongoose from "mongoose";
const Schema = mongoose.Schema;
import mongoosePaginate from 'mongoose-paginate';

const SubscriptionBillDetailsSchema = new mongoose.Schema({
    subscriptionBill: {
        type: Schema.Types.ObjectId,
        ref: 'SubscriptionBill'    },
    description: {
        type: String,
    },
    line_amount_ht: {
        type: String
    },
    deletedAt: {
        type: Date,
        default: null
    },
}, {
    timestamps: true,
    collation: { locale: 'fr', strength: 1 }
});
SubscriptionBillDetailsSchema.plugin(mongoosePaginate);

module.exports = mongoose.model("SubscriptionBillDetails", SubscriptionBillDetailsSchema);