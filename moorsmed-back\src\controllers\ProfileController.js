import Controller from './Controller';
import ProfileService from "../services/ProfileService";
import Profile from "../models/Profile";
const profileService = new ProfileService(Profile);

class ProfileController extends Controller {

  constructor(service) {
    super(service);
    this.addProfile = this.addProfile.bind(this);
    this.editProfile = this.editProfile.bind(this);
    this.findOneProfile = this.findOneProfile.bind(this);
    this.findProfiles = this.findProfiles.bind(this);
    this.deleteProfiles = this.deleteProfiles.bind(this);
    this.restoreProfiles = this.restoreProfiles.bind(this);
    this.getProfilePic = this.getProfilePic.bind(this);
    this.getPatients=this.getPatients.bind(this);
    this.patientEditPatient=this.patientEditPatient.bind(this);
    this.editLang=this.editLang.bind(this);
  }

  async addProfile(req) {
    return profileService.addProfile(req.body.profile, req.body.levelOfAccess, req.user, /*req.headers.referer.split("//")[2]|| 'localhost:4500'*/null);
  }
  async importProfiles(req) {
    return profileService.importProfiles(req.body.xlsx, req.user);
  }
  async editProfile(req) {
    return profileService.editProfile(req.body.profile, req.user,req.socketID);
  }
  async findOneProfile(req) {
    return profileService.findOneProfile(req.body.profileID, req.body.assignedID, req.body.email, req.user);
  }
  async findProfiles(req) {
    return profileService.findProfiles(req.body, req.body.page, req.body.limit, req.user);
  }
  async deleteProfiles(req) {
    return profileService.deleteProfiles(req.body.profileIDs, req.user);
  }
  async restoreProfiles(req) {
    return profileService.restoreProfiles(req.body.profileIDs, req.user);
  }
  async getProfilePic(req) {
    return profileService.getProfilePic(req.params.profileID);
  }
  async getPatients(req) {
    return profileService.getPatients(req.body, req.body.page, req.body.limit, req.user);
  }
  async patientEditPatient(req){
    return profileService.patientEditPatient(req.body.token,req.body.profile,req.body.files,req.socketID);
  }
  async patientGetPatient(req){
    return profileService.patientGetPatient(req.body.token);
  }
  async editLang(req){
    return profileService.editLang(req.body.lang,req.user)
  }
}

export default new ProfileController(profileService);