import Controller from './Controller';
import HospitalService from "../services/HospitalService";
import Hospital from "../models/Hospital";
const hospitalService = new HospitalService(Hospital);

class HospitalController extends Controller {

  constructor(service) {
    super(service);
    this.editHospital = this.editHospital.bind(this);
    this.getCities = this.getCities.bind(this);
    this.getCountries = this.getCountries.bind(this);
    this.exportData = this.exportData.bind(this);
  }

  async editHospital(req) {
    return hospitalService.editHospital(req.body.hospital,req.body.sessionTypes,req.user);
  }
  async getCities(req) {
    return hospitalService.getCities(req.body,req.user);
  }
  async getCountries(req) {
    return hospitalService.getCountries(req.body,req.user);
  }
  async exportData(req,res) {
    return hospitalService.exportData(res,req.body.fromDate,req.body.toDate,req.user);
  }

}

export default new HospitalController(hospitalService);