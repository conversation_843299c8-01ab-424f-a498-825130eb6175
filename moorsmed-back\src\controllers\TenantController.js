import Controller from './Controller';
import TenantService from "../services/TenantService";
import Tenant from "../models/Tenant";
const tenantService = new TenantService(Tenant);

class TenantController extends Controller {

  constructor(service) {
    super(service);
    this.manageTenant = this.manageTenant.bind(this);
    this.editTenant = this.editTenant.bind(this);
    this.getTenants = this.getTenants.bind(this);
    this.deleteTenant = this.deleteTenant.bind(this);
    this.getProducts = this.getProducts.bind(this);
    this.createOrEditProduct = this.createOrEditProduct.bind(this);
    this.deleteProduct = this.deleteProduct.bind(this);

  }

  async manageTenant(req) {
    return tenantService.manageTenant(req.body.tenant,req.body.profile,req.body.pack,req.body.hospital, req.user);
  }

  async editTenant(req) {
    return tenantService.editTenant(req.body, req.user);
  }

  async getTenants(req) {
    return tenantService.getTenants(req.body , req.user);
  }

  async deleteTenant(req) {
    return tenantService.deleteTenant(req.body.tenantID , req.user);
  }

  async getProducts(req) {
    return tenantService.getProducts(req.body , req.user);
  }

  async createOrEditProduct(req) {
    return tenantService.createOrEditProduct(req.body , req.user);
  }

  async deleteProduct(req) {
    return tenantService.deleteProduct(req.body , req.user);
  }

}

export default new TenantController(tenantService);