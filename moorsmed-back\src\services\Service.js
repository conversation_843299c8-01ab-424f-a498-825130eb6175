import mongoose from 'mongoose';
import APIError from '../errors/APIError';

class Service {
  constructor(model) {
    this.model = model;
    this.getAll = this.getAll.bind(this);
    this.insert = this.insert.bind(this);
    this.update = this.update.bind(this);
    this.delete = this.delete.bind(this);
    this.insertMany = this.insertMany.bind(this);
    this.updateMany = this.updateMany.bind(this);
    this.deleteMany = this.deleteMany.bind(this);
    this.findOne = this.findOne.bind(this);
    this.updateOne = this.updateOne.bind(this);
  }

  async getAll(query) {
    let { skip, limit } = query;

    skip = skip ? Number(skip) : 0;
    limit = limit ? Number(limit) : 10;

    delete query.skip;
    delete query.limit;

    if (query._id) {
      query._id = new mongoose.mongo.ObjectId(query._id);
    }

    const items = await this.model.find(query).skip(skip).limit(limit);
    const total = items.length;

    return {
      items,
      total
    };
  }

  async insert(data) {
    const item = await this.model.create(data);
    if (!item) {
      throw new APIError(500, 'Not able to create item');
    }

    return item;
  }

  async update(id, data) {
    const item = await this.model.findByIdAndUpdate(id, data, { new: true });
    if (!item) {
      throw new APIError(500, 'Not able to update item');
    }

    return item;
  }

  async delete(id) {
    const item = await this.model.findByIdAndDelete(id);
    if (!item) {
      throw new APIError(500, 'Not able to delete item');
    }

    return item;
  }

  async insertMany(array) {
    const items = await this.model.insertMany(array);
    if (!items) {
      throw new APIError(500, 'Not able to insert items');
    }

    return items;
  }

  async updateMany(filter, update) {
    const items = await this.model.updateMany(filter, update);
    if (!items) {
      throw new APIError(500, 'Not able to update items');
    }

    return items;
  }

  async deleteMany(filter) {
    const items = await this.model.deleteMany(filter);
    if (!items) {
      throw new APIError(500, 'Not able to delete items');
    }

    return items;
  }

  /**
   * Finds a single document by its _id field.
   * @param {ObjectId} id - value of _id to query by
   */
  async findOne(id) {
    const item = await this.model.findById(id);
    if (!item) {
      throw new APIError(500, 'Not able to find item');
    }

    return item;
  }

  async updateOne(filter, update) {
    const res = await this.model.updateOne(filter, update);
    if (res.n === 0 || res.nModified === 0) {
      throw new APIError(500, 'Not able to update items');
    }

    return res;
  }
}

export default Service;