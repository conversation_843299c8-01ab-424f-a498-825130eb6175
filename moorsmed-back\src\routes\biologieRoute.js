import BiologieController from '../controllers/BiologieController';
import { IS_LOGGED_IN } from '../middlewares/authenticators';

export default (createEndpoint => {
  createEndpoint({
    method: 'post',
    path: '/getBios',
    verify: [IS_LOGGED_IN],
    controller: BiologieController.getBios
  });
  createEndpoint({
    method: 'post',
    path: '/createOrEditBio',
    verify: [IS_LOGGED_IN],
    controller: BiologieController.createOrEditBio
  })
});