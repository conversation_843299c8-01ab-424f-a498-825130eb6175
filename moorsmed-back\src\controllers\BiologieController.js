import Controller from './Controller';
import BiologieService from "../services/BiologieService";
import Biologie from "../models/Biologie";

const biologieService = new BiologieService(Biologie);

class BiologieController extends Controller {

    constructor(service) {
      super(service)

      this.getBios = this.getBios.bind(this);
      this.createOrEditBio = this.createOrEditBio.bind(this);
    }

    async getBios(req) {
      return biologieService.getBios(req.body , req.user);
    }
    async createOrEditBio(req) {
      return biologieService.createOrEditBio(req.body, req.user);
    }


    
}

export default new BiologieController(biologieService);