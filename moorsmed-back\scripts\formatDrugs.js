const fs = require('fs');

// Read the input file
fs.readFile('./scripts/produits.txt', 'utf8', (err, data) => {
  if (err) {
    console.error(err);
    return;
  }

  // Split the data into an array of lines
  const lines = data.trim().split('\n');

  // Process each line
  const output = lines.map((line) => {
    const [name, price] = line.split('|').map((str) => str.trim());
    return { name, price };
  });

  // Write the output as JSON to a file
  fs.writeFile('./scripts/drugs.json', JSON.stringify(output), (err) => {
    if (err) {
      console.error(err);
      return;
    }
    console.log('Conversion successful');
  });
});
