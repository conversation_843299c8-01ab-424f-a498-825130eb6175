import Controller from './Controller';
import AppointmentService from "../services/AppointmentService";
import Appointment from "../models/Appointment";
const appointmentService = new AppointmentService(Appointment);

class AppointmentController extends Controller {

  constructor(service) {
    super(service);
    this.getAppointments = this.getAppointments.bind(this);
    this.createAppointment = this.createAppointment.bind(this);
    this.updateAppointment = this.updateAppointment.bind(this);
    this.updateMultipleAppointment = this.updateMultipleAppointment.bind(this);
    this.getIntervalAppointment = this.getIntervalAppointment.bind(this);
    this.doctorView = this.doctorView.bind(this);
    this.toCompleted=this.toCompleted.bind(this);
    this.waitingPatients=this.waitingPatients.bind(this);
    this.statesPerDay=this.statesPerDay.bind(this);
    this.createUpdateTimeoff=this.createUpdateTimeoff.bind(this);
    this.getTimeOffs=this.getTimeOffs.bind(this);
    this.timeProposition=this.timeProposition.bind(this);
    this.checkPlanning=this.checkPlanning.bind(this);
    this.deleteTimeoff=this.deleteTimeoff.bind(this);
    this.checkBeforeTimeoff=this.checkBeforeTimeoff.bind(this);
    this.doctorBadgeStat=this.doctorBadgeStat.bind(this);

  }

  async getAppointments(req) {
    return appointmentService.getAppointments(req.body, req.body.page, req.body.limit, req.user);
  }
  async createAppointment(req) {
    return appointmentService.createAppointment(req.body, req.user,req.body.socketID,req.headers.origin);
  }
  async updateAppointment(req) {
    return appointmentService.updateAppointment(req.body.appointmentID, req.body.appointment, req.user,req.body.socketID,req.headers.origin);
  }
  async updateMultipleAppointment(req) {
    return appointmentService.updateMultipleAppointment(req.body.filters, req.body.appointment, req.user);
  }
  async getIntervalAppointment(req) {
    return appointmentService.getIntervalAppointment(req.body, req.user);
  }
  async doctorView(req) {
    return appointmentService.doctorView(req.body.date, req.user);
  }
  async toCompleted(req) {
    return appointmentService.toCompleted(req.body.appointmentID, req.user,req.body.socketID);
  }
  async waitingPatients(req) {
    return appointmentService.waitingPatients(req.body, req.user);
  }
  async statesPerDay(req) {
    return appointmentService.statesPerDay(req.body, req.user);
  }
  async switchAppointment(req) {
    return appointmentService.switchAppointment(req.body.appointmentID1,req.body.appointmentID2, req.user,req.body.socketID);
  }
  async createUpdateTimeoff(req){
    return appointmentService.createUpdateTimeoff(req.body.timeoff,req.body.editAppointments,req.body.fromDaily,req.user,req.body.socketID);
  }
  async checkBeforeTimeoff(req){
    return appointmentService.checkBeforeTimeoff(req.body.startTime,req.body.endTime,req.body.doctor,req.user);
  }
  async deleteTimeoff(req){
    return appointmentService.deleteTimeoff(req.body.timeoffID,req.body.createdFromID,req.user,req.body.socketID);
  }
  async getTimeOffs(req){
    return appointmentService.getTimeOffs(req.body,req.user);
  }
  async timeProposition(req){
    return appointmentService.timeProposition(req.body,req.user);
  }
  async checkPlanning(req){
    return appointmentService.checkPlanning(req.body,req.user);
  }
  async deleteTimeoffFix(req){
    return appointmentService.deleteTimeoffFix(req.body.timeoffID,req.user,req.body.socketID);
  }
  async doctorBadgeStat(req){
    return appointmentService.doctorBadgeStat(req.user);
  }
  
  
}

export default new AppointmentController(appointmentService);