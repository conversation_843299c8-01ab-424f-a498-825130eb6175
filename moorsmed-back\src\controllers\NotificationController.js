import Controller from './Controller';
import NotificationService from "../services/NotificationService";
import Notification from "../models/Notification";
const notificationService = new NotificationService(Notification);

class NotificationController extends Controller {

  constructor(service) {
    super(service);
    this.getNotifications = this.getNotifications.bind(this);
    this.seenNotification = this.seenNotification.bind(this);
    this.unseenNotificationNumber=this.unseenNotificationNumber.bind(this);
  }

  async getNotifications(req) {
    return notificationService.getNotifications(req.body,req.user);
  }
  async seenNotification(req) {
    return notificationService.seenNotification(req.body.notificationIDs,req.user);
  }
  async unseenNotificationNumber(req){
    return notificationService.unseenNotificationNumber(req.body,req.user);
  }
  

}

export default new NotificationController(notificationService);