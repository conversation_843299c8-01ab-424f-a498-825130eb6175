import DrugController from '../controllers/DrugController';
import { IS_LOGGED_IN } from '../middlewares/authenticators';

export default (createEndpoint => {
  createEndpoint({
    method: 'post',
    path: '/getDrugFamilies',
    verify: [IS_LOGGED_IN],
    controller: DrugController.getDrugFamilies
  });
  createEndpoint({
    method: 'post',
    path: '/createOrEditDrugFamily',
    verify: [IS_LOGGED_IN],
    controller: DrugController.createOrEditDrugFamily
  }),
  createEndpoint({
    method: 'post',
    path: '/getDrugs',
    verify: [IS_LOGGED_IN],
    controller: DrugController.getDrugs
  });
  createEndpoint({
    method: 'post',
    path: '/createOrEditDrug',
    verify: [IS_LOGGED_IN],
    controller: DrugController.createOrEditDrug
  });
});