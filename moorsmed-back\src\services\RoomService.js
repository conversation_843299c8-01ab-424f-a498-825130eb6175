import Service from './Service';
import APIError from '../errors/APIError';
import mongoose, { mongo } from 'mongoose';
class RoomService extends Service {
    constructor(model) {
        super(model);
        this.createRoom = this.createRoom.bind(this);
        this.deleteRoom = this.deleteRoom.bind(this);
        this.editRoom = this.editRoom.bind(this);
        this.findOneRoom = this.findOneRoom.bind(this);
        this.findRooms = this.findRooms.bind(this);
    }
    async createRoom(room, user) {
        room.createdBy = user.profile._id;
        room.updatedBy = user.profile._id;
        room.hospital = user.profile.hospital._id;
        room = new this.model(room);
        room = await room.save();
        if (!room) throw new APIError(400, 'cannot create room');
        return room;
    }

    async deleteRoom(roomID, user) {
        let room = await this.model.findByIdAndDelete(roomID);
        if (!room) throw new APIError(400, 'cannot delete room');
        return room;
    }

    async editRoom(room, user) {
        room.updatedBy = user.profile._id;
        room = new this.model(room);
        room = await this.model.findByIdAndUpdate(room._id, room, { new: true });
        if (!room) throw new APIError(400, 'cannot update room');
        return room;
    }
    async findOneRoom(roomID, roomNumber, user) {
        let query = { hospital: mongoose.Types.ObjectId(user.profile.hospital._id) };
        if (!roomID && !roomNumber) throw new APIError(404, 'no filters provided');
        if (roomNumber) query.roomNumber = roomNumber;
        if (roomID) query._id = roomID;
        let room = await this.model.findOne(query);
        if (!room) throw new APIError(404, 'cannot find room');
        return room;
    }
    async findRooms(roomNumber, type, user) {
        let query = { hospital: mongoose.Types.ObjectId(user.profile.hospital._id) };
        if (roomNumber) query.roomNumber = roomNumber;
        if (type) query.type = type;

        let rooms = await this.model.find(query);
        if (!rooms) throw new APIError(404, 'cannot find rooms');
        return rooms;
    }
}

export default RoomService;