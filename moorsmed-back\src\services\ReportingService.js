import Service from './Service';
import APIError from '../errors/APIError';
import Session from '../models/Session';
import Invoice from '../models/Invoice';
import mongoose from 'mongoose';
import {groupBy} from './../helpers/dataOutput';
import {calculateDiff} from "./../helpers/reporting"
import moment from 'moment';
import { getDateFromTime} from "../helpers/dates";
import NodeCache from 'node-cache';

const ttl = 0; // cache for 1 Hour
export const cache = new NodeCache({ stdTTL: ttl, checkperiod: ttl * 0.2, useClones: false });
class ReportingService extends Service {
    constructor() {
        super(Session);
        this.getSessions=this.getSessions.bind(this);
        this.sessionTypesPie = this.sessionTypesPie.bind(this);
        this.sessionTypesLines = this.sessionTypesLines.bind(this);
        this.waitingTimeBars = this.waitingTimeBars.bind(this);
        this.dayOfWeeksSessions = this.dayOfWeeksSessions.bind(this);
        this.workedHours = this.workedHours.bind(this);
        this.ratesData=this.ratesData.bind(this);
    }

    async getSessions(fromDate,toDate,doctors,user,getInvoices=false){
        let query={
            hospital:mongoose.Types.ObjectId(user.profile.hospital._id),
        };
        if(fromDate && toDate){
            query.date={$gte:new Date(fromDate),$lte:new Date(toDate)};
        };
        if(doctors){
            query.doctor = {$in: doctors}
        }
        let sessions=await this.model.find(query).select('startTime endTime type date appointment').lean();
        sessions = sessions.filter(s => s.appointment.state === "COMPLETED");
        let sessionIDs=sessions.map(x=>x._id);
        let invoices=[];
        if(getInvoices)
        invoices=await Invoice.find({session:{$in:sessionIDs}}).select('session items total ').lean();
        sessions= sessions.map(x=>{
            x.type=x.supply.name;
            x.day=moment(x.date).format('YYYY-MM-DD');
            x.dayOfWeek=new Date(x.date).getDay();
            x.invoice=invoices.find(w=>w.session._id+""==x._id)
            return x;
        });
        return sessions;
    }

        async sessionTypesPie(fromDate,toDate,doctors,user) {
            let key= 'sessionTypesPie'+JSON.stringify({fromDate,toDate,doctors,hospital:user.profile.hospital._id});
            let data = await cache.get(key );
            if(data){
                return data;
            }else {
                let sessions=await this.getSessions(fromDate,toDate,doctors,user,true);
                sessions=await groupBy(sessions,['type']);
                let totalCount=0;
                let totalMoney=0;
                sessions=await sessions.map(type=>{
                    let count=0;
                    let money=0;
                    type.data.map(d=>{
                        count++;
                        totalCount++;
                        if(d.invoice && d.invoice.total){
                            totalMoney=totalMoney+d.invoice.total;
                            money=money+d.invoice.total;
                        }
                });
                type.count=count;
                type.money=money;
                delete type.data;
                return type
            })

            sessions=sessions.map(x=>{
                if(totalCount>0) 
                x.countPerc=Number((x.count/totalCount)*100).toFixed(2);
                if(totalMoney>0) 
                x.moneyPerc=Number((x.money/totalMoney)*100).toFixed(2);
                return x;
            })

                await cache.set(key, {types:sessions,count:totalCount,money:totalMoney} )
                return {types:sessions,count:totalCount,money:totalMoney}
            }
            
            
          }
          async dayOfWeeksSessions(fromDate,toDate,doctors,user) {
            let key='dayOfWeeksSessions'+ JSON.stringify({fromDate,toDate,doctors,hospital:user.profile.hospital._id});
            let data = await cache.get(key );
            if(data){
           return data;
            }else {
            let sessions=await this.getSessions(fromDate,toDate,doctors,user);
            sessions= groupBy(sessions,['dayOfWeek','day']);

            sessions=sessions.map(x=>{
                let count=0;
                x.data.map(d=>{
                    count++;
                });
                x.count=count;     
                delete x.data;    
                return x;
            })

            sessions= groupBy(sessions,['dayOfWeek']);           
            sessions=sessions.map(x=>{
                let count=0;
                x.data.map(d=>{
                    count=count+d.count;
                });
                if(x.data.length>0)
                x.count=Number(count/x.data.length).toFixed(0);     
                else x.count=0
                delete x.data;    
                return x;
            })

            let result=[1,2,3,4,5,6,0].map((x,i)=>{
                return {
                    dayOfWeek:x,
                    date: new Date(moment("2000-01-03").add(i, 'days')),
                    count:(sessions.find(d=>d.dayOfWeek+""==x+"")||{count:0}).count
                }
            })
            await cache.set(key, result)
            return  result;
        }
        }

          async sessionTypesLines(fromDate,toDate,doctors,user) {
            let key= 'sessionTypesLines'+JSON.stringify({fromDate,toDate,doctors,hospital:user.profile.hospital._id});
            let data = await cache.get(key );
            if(data){
           return data;
            }else {
            let sessions=await this.getSessions(fromDate,toDate,doctors,user,true);
            let generalData=groupBy(sessions,['day']).map(x=>{
                x.type="Général";
                return x;
            });
            sessions= groupBy(sessions,['type','day']);

            sessions=sessions.concat(generalData);
            sessions=sessions.map(x=>{
                x.date=moment(x.date).format('YYYY-MM-DD');
                let count=0;
                let money=0;
                let avgDuration=0;
                x.data.map(d=>{
                    count++;
                    if(d.invoice && d.invoice.total){
                        money=money+d.invoice.total;
                    }
                    avgDuration=avgDuration+((new Date(d.endTime).getTime()-new Date(d.startTime).getTime())/(60*1000))
                });
                delete x.data;
                delete x.date;
                if(count>0)
                x.avgDuration=avgDuration/count;
                else                 
                x.avgDuration=0;
                x.money=money;
                x.count=count;
                return x;
            })
        let result=groupBy(sessions,['type']);
        await cache.set(key, result )
            return  result;
        }
        }
        async waitingTimeBars(fromDate,toDate,doctors,user) {
            let key= 'waitingTimeBars'+JSON.stringify({fromDate,toDate,doctors,hospital:user.profile.hospital._id});
            let data = await cache.get(key );
            if(data){
           return data;
            }else {
            let sessionQuery={
                hospital:mongoose.Types.ObjectId(user.profile.hospital._id),
            }
            if(fromDate && toDate){
                sessionQuery.date={$gte:new Date(fromDate),$lte:new Date(toDate)};
            }
            if(doctors){
                sessionQuery.doctor = {$in: doctors}
            }
            let sessions=await this.model.find(sessionQuery).select("appointment startTime date").lean();
            sessions = sessions.filter(s => s.appointment.state === "COMPLETED");
            sessions=sessions.map(x=>{
                x.day=moment(x.date).format('YYYY-MM-DD');
                return x;
            })

            sessions=groupBy(sessions,['day']);
            sessions=sessions.map(x=>{
                let waitingTime=0;
                let beetween=0;

                x.waitingTime=waitingTime;
                x.beetween=beetween;

                let count=0;
                x.data.map(w=>{
                    if(w.appointment.patientArrived){
                        count++;
                        waitingTime=waitingTime+(new Date(w.startTime).getTime()-new Date(w.appointment.patientArrived).getTime())/(1000*60);
                    }
                    if( new Date(w.startTime).getTime()>new Date(w.appointment.startTime).getTime()){
                        beetween=beetween+(new Date(w.startTime).getTime()-new Date(w.appointment.startTime).getTime())/(1000*60);
                    }
                })
                if(count>0){
                    x.waitingTime=waitingTime/count;
                    x.beetween=beetween/count;
                }
                delete x.data;
                return x;
            })
            let result=['Temps d\'attente moyen','Temps de retard'].map((x,i)=>{
                return {
                    title:x,
                    data:sessions.map((a)=>{return {day:a.day,value:(i==0)?a.waitingTime:a.beetween}})
                }
            })
            await cache.set(key, result )
            return result;
        }
        }
        async workedHours(fromDate,toDate,doctors,user){
            let key= 'workedHours'+JSON.stringify({fromDate,toDate,doctors,hospital:user.profile.hospital._id});
            let data = await cache.get(key );
            if(data){
           return data;
            }else {
            let sessions=await this.getSessions(fromDate,toDate,doctors,user);
            let schedules=user.profile.hospital.schedules;
            sessions=await groupBy(sessions,['day']);
            let occupation=sessions;
             occupation=occupation.map(x=>{
                let duration=0;
                let count=0;
                x.data.map(d=>{
                    if( new Date(d.endTime).getTime() > new Date(d.startTime).getTime() ){
                        duration=duration+(new Date(d.endTime).getTime()-new Date(d.startTime).getTime())/(1000*3600);
                        count++;
                    }
                })
                let first,last;

                if(x.data.length>0 && x.data[0].startTime && x.data[x.data.length-1].endTime){
                     first=x.data[0].startTime;
                     last=x.data[x.data.length-1].endTime;
                }
                else return ;
                let schedule=schedules.find(x=>x.day+''==new Date(x.day).getDay()+'');
                let startBreak=getDateFromTime(x.day,schedule.startBreak);
                let startTime=getDateFromTime(x.day,schedule.startTime);
                let endTime=getDateFromTime(x.day,schedule.endTime);
                let endBreak=getDateFromTime(x.day,schedule.endBreak);
                x.hours=0;
                x.hours=duration;
                x.opening=((new Date(endTime).getTime()-new Date(startTime).getTime()))/(1000*3600);
                    return x;
            }).filter(x=>x&& x.day);

            let efficiency ={title:"Taux d'efficacité",data:occupation.map(o=>{


                 return {day:o.day,efficiency:o.opening?(o.hours/o.opening)*100:0};
            })}
            let opening=occupation.map(x=>{
                return {
                    day:x.day,hours:x.opening
                }
            })
            await cache.set(key,[{title:"Heures d'ouvertures",data:opening},{title:"Heures d'occupations",data:occupation},efficiency])

            return [{title:"Heures d'ouvertures",data:opening},{title:"Heures d'occupations",data:occupation},efficiency];
       
         } }
        async ratesData(fromDate,toDate,doctors,user){
            let key= 'ratesData'+JSON.stringify({fromDate,toDate,doctors,hospital:user.profile.hospital._id});
            let data = await cache.get(key);
            if(data){
                return data;
            }else {
                let dayNumber=moment(toDate).diff(moment(fromDate), 'days')
                let prevFrom=moment(fromDate).add(-1*dayNumber,"days").format("YYYY-MM-DD");
                let prevto=moment(toDate).add(-1*dayNumber,"days").format("YYYY-MM-DD");
                let result={};
                let ratesData=[];
                let promise1=new Promise(async (resolve, reject) => {
                    let countMoney=await this.sessionTypesPie(fromDate,toDate,doctors,user);
                    let countMoneyOld=await this.sessionTypesPie(new Date(prevFrom),new Date(prevto),doctors,user);
                    ratesData.push({name:"count",value:{value:countMoney.count,diff:calculateDiff(countMoney.count,countMoneyOld.count)}}) ;
                    ratesData.push({name:"money",value:{value:countMoney.money,diff:calculateDiff(countMoney.money,countMoneyOld.money)}}) ;
                    resolve(ratesData) 
                });
                let promise2=new Promise(async (resolve, reject) => {
                    let waitingTime=await this.waitingTimeBars(fromDate,toDate,doctors,user);
                    let waitingTimeOld=await this.waitingTimeBars(new Date(prevFrom),new Date(prevto),doctors,user);
                    let waitingTimeAvg=0;
                    waitingTime[0].data.map(x=>{
                        waitingTimeAvg=waitingTimeAvg+x.value;
                    });
                    if(waitingTime[0].data.length>0)waitingTimeAvg=waitingTimeAvg/waitingTime[0].data.length;
                    let waitingTimeAvgOld=0;
                    waitingTimeOld[0].data.map(x=>{
                        waitingTimeAvgOld=waitingTimeAvgOld+x.value;
                    });
                    if(waitingTimeOld[0].data.length>0)waitingTimeAvgOld=waitingTimeAvgOld/waitingTimeOld[0].data.length;
                    ratesData.push({name:"waitingTime",value:{value:waitingTimeAvg,diff:calculateDiff(waitingTimeAvg,waitingTimeAvgOld)}}) ;

                
                    resolve(ratesData) 
                });
                let promise3=new Promise(async (resolve, reject) => {
                    let waitingTime=await this.workedHours(fromDate,toDate,doctors,user);
                    let waitingTimeOld=await this.workedHours(new Date(prevFrom),new Date(prevto),doctors,user);
                    let waitingTimeAvg=0;
                    waitingTime[2].data.map(x=>{
                        waitingTimeAvg=waitingTimeAvg+x.efficiency;
                    });
                    if(waitingTime[2].data.length>0)waitingTimeAvg=waitingTimeAvg/waitingTime[2].data.length;
                    let waitingTimeAvgOld=0;
                    waitingTimeOld[2].data.map(x=>{
                        waitingTimeAvgOld=waitingTimeAvgOld+x.efficiency;
                    });
                    if(waitingTimeOld[2].data.length>0)waitingTimeAvgOld=waitingTimeAvgOld/waitingTimeOld[2].data.length;
                    ratesData.push({name:"efficiency",value:{value:waitingTimeAvg,diff:calculateDiff(waitingTimeAvg,waitingTimeAvgOld)}}) ;

                
                    resolve(ratesData) 
                });

             await Promise.all([promise1,promise2,promise3]).then((values) => {

              });
              let finalRates={};
              ratesData.map(x=>{
                  finalRates[x.name]=x.value;
              })

              await cache.set(key, finalRates)

              return finalRates;

        }}
}

export default ReportingService;