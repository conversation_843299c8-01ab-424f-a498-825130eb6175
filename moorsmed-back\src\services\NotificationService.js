import Service from './Service';
import APIError from '../errors/APIError';
import Notification from '../models/Notification';
import mongoose from 'mongoose';

class NotificationService extends Service {
    constructor(model) {
        super(model);
        this.getNotifications = this.getNotifications.bind(this);
        this.queryNotification=this.queryNotification.bind(this);    
        this.seenNotification=this.seenNotification.bind(this);
        this.unseenNotificationNumber=this.unseenNotificationNumber.bind(this)
    }
    async queryNotification(query={},filters) {
        if (filters.types) {
            query.type={$in:filters.types};
        }
        return query;
    }

        async getNotifications(filters,user) {
            let query={receiver:mongoose.Types.ObjectId(user.profile._id),hospital:mongoose.Types.ObjectId(user.profile.hospital._id)};
        query=await this.queryNotification(query,filters);
        let options = {
            sort: {createdAt:-1},
            page: parseInt(filters.page, 10) || 1,
            limit: parseInt(filters.limit, 10) || 5,
        };
        let notifications = await Notification.paginate(query, options);
        if (!notifications) throw new APIError(404, 'cannot find notifications');
        return notifications;

          }
          async seenNotification(notificationIDs,user){
              let query={
                receiver:mongoose.Types.ObjectId(user.profile._id),
                hospital:mongoose.Types.ObjectId(user.profile.hospital._id),
                $or:[{seenDate:null},{seenDate:{$exists: false }}]
            };
            if(notificationIDs && notificationIDs.length>0)
            query._id={$in:notificationIDs};
              let notifs=await this.model.updateMany(query,{seenDate:Date.now()},{new:true});

              if(!notifs){
                throw new APIError(404, 'cannot find notifications');
              }
              return notifs;
          }
          async unseenNotificationNumber(filters,user){
              let number=0;
              let query= {
                receiver:mongoose.Types.ObjectId(user.profile._id),
                  hospital:mongoose.Types.ObjectId(user.profile.hospital._id),
              $or:[{seenDate:null},{seenDate:{$exists: false }}]};
              query=await this.queryNotification(query,filters);
              let notifications=await this.model.find(query);
              if(!notifications)  throw new APIError(404, 'cannot find notifications');
              return notifications.length;
          }


}

export default NotificationService;