import errorHandler from "../../config/utils/errorHandler";

const multer = require('multer');
const azureStorage = require('azure-storage');
const getStream = require('into-stream');
import APIError from '../errors/APIError';
require('dotenv').config({ path: process.env.NODE_ENV ? `environments/.env.${process.env.NODE_ENV}` : "environments/.env.dev" });

const upload = multer({
    storage: multer.memoryStorage()
    // file size limitation in bytes
    //limits: { fileSize: ******** },
});

// const azureStorageConfig = {
//     accountName: process.env.azureStorageAccountName,
//     accountKey:process.env.azureStorageAccountKey,
//     blobURL: process.env.azureStorageBlobURL,
//     containerName:process.env.azureStorageContainerName
// };
// const uploadFileToBlob = async (directoryPath, file, hospital) => {
//     if (!hospital) hospital = 'anonym';
//     return new Promise((resolve, reject) => {
//
//         const blobName = new Date().getTime() + '-' + file.originalname;
//         const stream = getStream(file.buffer);
//         const streamLength = file.buffer.length;
//
//         const blobService = azureStorage.createBlobService(azureStorageConfig.accountName, azureStorageConfig.accountKey);
//         blobService.createBlockBlobFromStream(azureStorageConfig.containerName, `${hospital}/${directoryPath}/${blobName}`, stream, streamLength, err => {
//             if (err) {
//                 reject(err);
//             } else {
//                 resolve({ filename: blobName,
//                     originalname: file.originalname + '-' + new Date(),
//                     mimetype: file.mimetype,
//                     size: file.length,
//                     path: `${azureStorageConfig.containerName}/${directoryPath}/${blobName}`,
//                     downloadUrl: 'https://' + azureStorageConfig.accountName + '.blob.core.windows.net/' + azureStorageConfig.containerName + '/' + `${hospital}/${directoryPath}/${blobName}`,
//
//                     url: `${azureStorageConfig.blobURL}${azureStorageConfig.containerName}/${directoryPath}/${blobName}`
//                 });
//             }
//         });
//     });
// };
// const uploadFilesToBlob = async (directoryPath, files, hospital) => {
//     if (!hospital) hospital = '';
//     let promises = files.map(x => uploadFileToBlob(directoryPath, x, hospital));
//     return Promise.all(promises).then(values => {
//         return values;
//     }).catch(reason => {
//         console.log(reason);
//         return;
//     });
// };

// const getBlobName = originalName => {
//     const identifier = Math.random().toString().replace(/0\./, ''); // remove "0." from start of string
//     return `${identifier}-${originalName}`;
// };
//
// const profilePictureUpload = async (req, res) => {
//         const profilePicture = await uploadFileToBlob('profilePictures', req.file, req.user.profile.hospital._id);
// if(!profilePicture)throw new APIError(403, 'cannot upload');
// return profilePicture;
//
// };
// const docUpload = async (req, res) => {
//         const profilePicture = await uploadFileToBlob('docs', req.file, req.user.profile.hospital._id);
//         if(!profilePicture)throw new APIError(403, 'cannot upload');
//
//         return profilePicture;
//
// };
// const docsUpload = async (req, res) => {
//         const profilePicture = await uploadFilesToBlob('docs', req.files, req.user.profile.hospital._id);
//         if(!profilePicture)throw new APIError(403, 'cannot upload');
//         return profilePicture;
// };

import { IS_LOGGED_IN, PATIENT_IS_LOGGED_IN } from '../middlewares/authenticators';

const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, process.env.FILES_PATH)
    },
    filename: function (req, file, cb) {
        req.specialName = new Date().getTime() + '-' + file.originalname;
        cb(null, req.specialName);
    }
})

const uploadFile = multer({ storage: storage }).single('file');

const handleSingleUploadFile = async (req, res) => {
    // const hospital = req.user.profile.hospital._id;
    return new Promise((resolve, reject) => {
        uploadFile(req, res, (error) => {
            const file = req.file;

            var blobName = req.specialName;

            // return res.status(200).json({
            //     filename: blobName,
            //     originalname: req.file.originalname,
            //     mimetype: req.file.mimetype,
            //     size: req.file.length || 1554,
            //     path: 'hello',
            //     downloadUrl: 'https://www.google.com/fjfj',
            //     url: 'https://www.google.com/fjfj'
            // })
            if (error) {
                reject(error);
            }
            resolve({
                filename: blobName,
                originalname: file.originalname + '-' + new Date(),
                mimetype: file.mimetype,
                size: file.length,
                downloadUrl: process.env.BACKEND_URL + 'files/' + blobName,
                url: process.env.BACKEND_URL + 'files/' + blobName
            });
        });
    });
};

var myDocUpload = async function docsUpload(req, res) {
    var file = await handleSingleUploadFile(req, res);
    if (!file) throw new APIError(403, 'cannot upload');

    return file;
};

export default (createEndpoint => {
    createEndpoint({
        method: 'post',
        path: '/profilePicture',
        verify: [PATIENT_IS_LOGGED_IN],
        controller: myDocUpload
    });


    createEndpoint({
        method: 'post',
        path: '/doc',
        verify: [PATIENT_IS_LOGGED_IN],
        controller: myDocUpload
    });

    createEndpoint({
        method: 'post',
        path: '/docs',
        verify: [PATIENT_IS_LOGGED_IN],
        controller: myDocUpload
    });
    
});
