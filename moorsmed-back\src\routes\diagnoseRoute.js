import Diagnose<PERSON>ontroller from '../controllers/DiagnoseController';
import { IS_LOGGED_IN } from '../middlewares/authenticators';

export default (createEndpoint => {
  createEndpoint({
    method: 'post',
    path: '/getDiagnoses',
    verify: [IS_LOGGED_IN],
    controller: DiagnoseController.getDiagnoses
  });
  createEndpoint({
    method: 'post',
    path: '/createOrUpdateDiagnose',
    verify: [IS_LOGGED_IN],
    controller: DiagnoseController.createOrUpdateDiagnose
  });
  createEndpoint({
    method: 'post',
    path: '/deleteDiagnoses',
    verify: [IS_LOGGED_IN],
    controller: DiagnoseController.deleteDiagnoses
  });
  createEndpoint({
    method: 'post',
    path: '/initDiagnoses',
    verify: [IS_LOGGED_IN],
    controller: DiagnoseController.initDiagnoses
  });
});