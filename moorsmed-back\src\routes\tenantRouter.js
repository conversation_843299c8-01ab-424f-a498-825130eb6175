import TenantController from '../controllers/TenantController';
import { IS_MANAGER_HOSPITAL } from '../middlewares/authenticators';

export default (createEndpoint => {
  createEndpoint({
    method: 'post',
    path: '/manageTenant',
    verify: [IS_MANAGER_HOSPITAL],
    controller: TenantController.manageTenant
  });
  createEndpoint({
    method: 'post',
    path: '/getTenants',
    verify: [IS_MANAGER_HOSPITAL],
    controller: TenantController.getTenants
  });
  createEndpoint({
    method: 'post',
    path: '/editTenant',
    verify: [ IS_MANAGER_HOSPITAL],
    controller: TenantController.editTenant
  });
  createEndpoint({
    method: 'post',
    path: '/delete',
    verify: [ IS_MANAGER_HOSPITAL],
    controller: TenantController.deleteTenant
  });
  createEndpoint({
    method: 'post',
    path: '/getProducts',
    verify: [IS_MANAGER_HOSPITAL],
    controller: TenantController.getProducts
  });

  createEndpoint({
    method: 'post',
    path: '/createOrEditProduct',
    verify: [IS_MANAGER_HOSPITAL],
    controller: TenantController.createOrEditProduct
  });

  createEndpoint({
    method: 'post',
    path: '/deleteProduct',
    verify: [IS_MANAGER_HOSPITAL],
    controller: TenantController.deleteProduct
  });
});