import RadiographController from '../controllers/RadiographController';
import { IS_LOGGED_IN } from '../middlewares/authenticators';

export default (createEndpoint => {
  createEndpoint({
      method: 'post',
      path: '/getRadios',
      verify: [IS_LOGGED_IN],
      controller: RadiographController.getRadios
    });
  createEndpoint({
    method: 'post',
    path: '/createOrEditRadio',
    verify: [IS_LOGGED_IN],
    controller: RadiographController.createOrEditRadio
  }),
  createEndpoint({
    method: 'post',
    path: '/getRadioFamilies',
    verify: [IS_LOGGED_IN],
    controller: RadiographController.getRadioFamilies
  });
  createEndpoint({
    method: 'post',
    path: '/createOrEditRadioFamily',
    verify: [IS_LOGGED_IN],
    controller: RadiographController.createOrEditRadioFamily
  })
});