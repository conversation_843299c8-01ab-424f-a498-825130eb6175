{"swagger": "2.0", "paths": {"/api/users/signup": {"post": {"summary": "/api/users/signup", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["User"]}}, "/api/users/signin": {"post": {"summary": "/api/users/signin", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["User"]}}, "/api/users/signout": {"post": {"summary": "/api/users/signout", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["User"]}}, "/api/users/edituser": {"put": {"summary": "/api/users/edituser", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["User"]}}, "/api/users/resetpassword": {"post": {"summary": "/api/users/resetpassword", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["User"]}}, "/api/users/changepassword": {"post": {"summary": "/api/users/changepassword", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["User"]}}, "/api/users/isemailtaken": {"post": {"summary": "/api/users/isemailtaken", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["User"]}}, "/api/users/refreshUserStorage": {"post": {"summary": "/api/users/refreshUserStorage", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["User"]}}, "/api/users": {"get": {"summary": "/api/users", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["User"]}}, "/api/profiles/create": {"post": {"summary": "/api/profiles/create", "consumes": ["application/json"], "parameters": [], "responses": {"200": {"description": "OK", "schema": {"type": "object", "properties": {"deletedAt": {"type": "null", "example": null}, "doctors": {"type": "array", "items": {"type": "string"}, "example": ["68359c73517d592fec5c1726"]}, "receptionits": {"type": "array", "items": {}, "example": [null]}, "_id": {"type": "string", "example": "68359c73517d592fec5c1725"}, "title": {"type": "string", "example": "DOCTOR"}, "createdAt": {"type": "string", "example": "2025-05-27T11:05:24.115Z"}, "updatedAt": {"type": "string", "example": "2025-05-27T11:05:24.115Z"}, "__v": {"type": "number", "example": 0}, "staffId": {"type": "string", "example": "68359c73517d592fec5c1726"}, "phoneNumber": {"type": "string", "example": "0677873819"}, "email": {"type": "string", "example": "<EMAIL>"}, "adress": {"type": "string", "example": "356 av dakhla  c d casa"}, "firstName": {"type": "string", "example": "OUHNA"}, "lastName": {"type": "string", "example": "ABDERRAHMANE"}, "startingDate": {"type": "string", "example": "2025-05-27T11:04:50.156Z"}, "profilePic": {"type": "string", "example": ""}, "language": {"type": "string", "example": "fr"}, "gender": {"type": "string", "example": "MALE"}, "birthDate": {"type": "string", "example": "2025-05-27T11:05:17.661Z"}, "height": {"type": "null", "example": null}, "weight": {"type": "null", "example": null}, "createdBy": {"type": "string", "example": "682b029a2fa88e753eb8f746"}, "updatedBy": {"type": "string", "example": "682b029a2fa88e753eb8f746"}, "hospital": {"type": "string", "example": "682b029a2fa88e620ab8f73b"}}}}}, "tags": ["Profile"], "produces": ["application/json"]}}, "/api/profiles/find": {"post": {"summary": "/api/profiles/find", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Profile"]}}, "/api/profiles/findOne": {"post": {"summary": "/api/profiles/findOne", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Profile"]}}, "/api/profiles/edit": {"post": {"summary": "/api/profiles/edit", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Profile"]}}, "/api/profiles/delete": {"post": {"summary": "/api/profiles/delete", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Profile"]}}, "/api/profiles/restore": {"post": {"summary": "/api/profiles/restore", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Profile"]}}, "/api/profiles/getProfilePic/{profileID}": {"get": {"summary": "/api/profiles/getProfilePic/{profileID}", "consumes": ["application/json"], "parameters": [{"name": "profileID", "in": "path", "required": true}], "responses": {}, "tags": ["Profile"]}}, "/api/profiles/getPatients": {"post": {"summary": "/api/profiles/getPatients", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Patient", "Profile"]}}, "/api/profiles/patientEditPatient": {"post": {"summary": "/api/profiles/patientEditPatient", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Patient", "Profile"]}}, "/api/profiles/patientGetPatient": {"post": {"summary": "/api/profiles/patientGetPatient", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Patient", "Profile"]}}, "/api/profiles/editLang": {"post": {"summary": "/api/profiles/editLang", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Profile"]}}, "/api/rooms/create": {"post": {"summary": "/api/rooms/create", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Room"]}}, "/api/rooms/delete/{roomID}": {"delete": {"summary": "/api/rooms/delete/{roomID}", "consumes": ["application/json"], "parameters": [{"name": "roomID", "in": "path", "required": true}], "responses": {}, "tags": ["Room"]}}, "/api/rooms/edit": {"put": {"summary": "/api/rooms/edit", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Room"]}}, "/api/rooms/findOne": {"post": {"summary": "/api/rooms/findOne", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Room"]}}, "/api/rooms/find": {"post": {"summary": "/api/rooms/find", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Room"]}}, "/api/specialties/create": {"post": {"summary": "/api/specialties/create", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": []}}, "/api/specialties/delete/{specialtyID}": {"delete": {"summary": "/api/specialties/delete/{specialtyID}", "consumes": ["application/json"], "parameters": [{"name": "specialtyID", "in": "path", "required": true}], "responses": {}, "tags": ["Specialty"]}}, "/api/specialties/edit": {"put": {"summary": "/api/specialties/edit", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": []}}, "/api/specialties/findOne": {"post": {"summary": "/api/specialties/findOne", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": []}}, "/api/specialties/find": {"post": {"summary": "/api/specialties/find", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": []}}, "/api/hospitals/editHospital": {"post": {"summary": "/api/hospitals/editHospital", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Hospital"]}}, "/api/hospitals/getCities": {"post": {"summary": "/api/hospitals/getCities", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Hospital"]}}, "/api/hospitals/getCountries": {"post": {"summary": "/api/hospitals/getCountries", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Hospital"]}}, "/api/hospitals/exportData": {"post": {"summary": "/api/hospitals/exportData", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Hospital"]}}, "/api/appointments/getAppointments": {"post": {"summary": "/api/appointments/getAppointments", "consumes": ["application/json"], "parameters": [], "responses": {"200": {"description": "OK", "schema": {"type": "object", "properties": {"docs": {"type": "array", "items": {}, "example": [null]}, "total": {"type": "number", "example": 0}, "limit": {"type": "number", "example": 4}, "page": {"type": "number", "example": 1}, "pages": {"type": "number", "example": 1}}}}}, "tags": ["Appointment"], "produces": ["application/json"]}}, "/api/appointments/createAppointment": {"post": {"summary": "/api/appointments/createAppointment", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Appointment"]}}, "/api/appointments/updateAppointment": {"post": {"summary": "/api/appointments/updateAppointment", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Appointment"]}}, "/api/appointments/updateMultipleAppointment": {"post": {"summary": "/api/appointments/updateMultipleAppointment", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Appointment"]}}, "/api/appointments/getIntervalAppointment": {"post": {"summary": "/api/appointments/getIntervalAppointment", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Appointment"]}}, "/api/appointments/doctorView": {"post": {"summary": "/api/appointments/doctorView", "consumes": ["application/json"], "parameters": [], "responses": {"200": {"description": "OK", "schema": {"type": "object", "properties": {"AvgWaitingTime": {"type": "null", "example": null}, "patientsWaitingNumber": {"type": "number", "example": 0}, "nextAppointment": {"type": "null", "example": null}}}}}, "tags": ["Appointment"], "produces": ["application/json"]}}, "/api/appointments/toCompleted": {"post": {"summary": "/api/appointments/toCompleted", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Appointment"]}}, "/api/appointments/waitingPatients": {"post": {"summary": "/api/appointments/waitingPatients", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Patient", "Appointment"]}}, "/api/appointments/statesPerDay": {"post": {"summary": "/api/appointments/statesPerDay", "consumes": ["application/json"], "parameters": [], "responses": {"200": {"description": "OK", "schema": {"type": "object", "properties": {"waitingAppointments": {"type": "number", "example": 0}, "doneAppointments": {"type": "number", "example": 0}, "averageWaitingTime": {"type": "number", "example": 0}, "waitingPatients": {"type": "number", "example": 0}}}}}, "tags": ["Appointment"], "produces": ["application/json"]}}, "/api/appointments/switchAppointment": {"post": {"summary": "/api/appointments/switchAppointment", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Appointment"]}}, "/api/appointments/createUpdateTimeoff": {"post": {"summary": "/api/appointments/createUpdateTimeoff", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Appointment", "Timeoff"]}}, "/api/appointments/deleteTimeoff": {"post": {"summary": "/api/appointments/deleteTimeoff", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Appointment", "Timeoff"]}}, "/api/appointments/getTimeOffs": {"post": {"summary": "/api/appointments/getTimeOffs", "consumes": ["application/json"], "parameters": [], "responses": {"200": {"description": "OK", "schema": {"type": "object", "properties": {"timeoffs": {"type": "array", "items": {}, "example": [null]}}}}}, "tags": ["Appointment", "Timeoff"], "produces": ["application/json"]}}, "/api/appointments/timeProposition": {"post": {"summary": "/api/appointments/timeProposition", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Appointment"]}}, "/api/appointments/checkPlanning": {"post": {"summary": "/api/appointments/checkPlanning", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Appointment"]}}, "/api/appointments/checkBeforeTimeoff": {"post": {"summary": "/api/appointments/checkBeforeTimeoff", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Appointment", "Timeoff"]}}, "/api/appointments/deleteTimeoffFix": {"post": {"summary": "/api/appointments/deleteTimeoffFix", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Appointment", "Timeoff"]}}, "/api/appointments/doctorBadgeStat": {"post": {"summary": "/api/appointments/doctorBadgeStat", "consumes": ["application/json"], "parameters": [], "responses": {"200": {"description": "OK", "schema": {"type": "object", "properties": {"patientAtOffice": {"type": "number", "example": 0}, "inProgressAppointments": {"type": "number", "example": 0}}}}}, "tags": ["Appointment"], "produces": ["application/json"]}}, "/api/sessions/getSessions": {"post": {"summary": "/api/sessions/getSessions", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Session"]}}, "/api/sessions/createSession": {"post": {"summary": "/api/sessions/createSession", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Session"]}}, "/api/sessions/updateSession": {"post": {"summary": "/api/sessions/updateSession", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Session"]}}, "/api/sessions/detailSession": {"post": {"summary": "/api/sessions/detailSession", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Session"]}}, "/api/sessions/createOrEditNote": {"post": {"summary": "/api/sessions/createOrEditNote", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Session"]}}, "/api/sessions/deleteNote": {"post": {"summary": "/api/sessions/deleteNote", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Session"]}}, "/api/sessions/createOrEditDrug": {"post": {"summary": "/api/sessions/createOrEditDrug", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Session", "Drug"]}}, "/api/sessions/deleteDrug": {"post": {"summary": "/api/sessions/deleteDrug", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Session", "Drug"]}}, "/api/sessions/addDiagnose": {"post": {"summary": "/api/sessions/addDiagnose", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Session", "Diagnose"]}}, "/api/sessions/deleteDiagnose": {"post": {"summary": "/api/sessions/deleteDiagnose", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Session", "Diagnose"]}}, "/api/sessions/toAlmostCompleted": {"post": {"summary": "/api/sessions/toAlmostCompleted", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Session"]}}, "/api/sessions/averageTime": {"post": {"summary": "/api/sessions/averageTime", "consumes": ["application/json"], "parameters": [], "responses": {"200": {"description": "OK", "schema": {"type": "object", "properties": {"workedHours": {"type": "number", "example": 0}, "totalAvgSessionTime": {"type": "number", "example": 0}, "avgSessionTimePerType": {"type": "array", "items": {"type": "object", "properties": {"avgSessionTime": {"type": "number"}, "workedHours": {"type": "number"}}, "required": ["avgSessionTime", "workedHours"]}, "example": [{"avgSessionTime": 0, "workedHours": 0}]}}}}}, "tags": ["Session"], "produces": ["application/json"]}}, "/api/sessions/createOrEditPrescriptionPage": {"post": {"summary": "/api/sessions/createOrEditPrescriptionPage", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Session", "Prescription"]}}, "/api/sessions/deletePrescriptionPage": {"post": {"summary": "/api/sessions/deletePrescriptionPage", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Session", "Prescription"]}}, "/api/diagnoses/getDiagnoses": {"post": {"summary": "/api/diagnoses/getDiagnoses", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Diagnose"]}}, "/api/diagnoses/createOrUpdateDiagnose": {"post": {"summary": "/api/diagnoses/createOrUpdateDiagnose", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Diagnose"]}}, "/api/diagnoses/deleteDiagnoses": {"post": {"summary": "/api/diagnoses/deleteDiagnoses", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Diagnose"]}}, "/api/diagnoses/initDiagnoses": {"post": {"summary": "/api/diagnoses/initDiagnoses", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Diagnose"]}}, "/api/store/profilePicture": {"post": {"summary": "/api/store/profilePicture", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Profile"]}}, "/api/store/doc": {"post": {"summary": "/api/store/doc", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": []}}, "/api/store/docs": {"post": {"summary": "/api/store/docs", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": []}}, "/api/supplies/getSupplies": {"post": {"summary": "/api/supplies/getSupplies", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": []}}, "/api/supplies/createOrUpdateSupply": {"post": {"summary": "/api/supplies/createOrUpdateSupply", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Supply"]}}, "/api/supplies/deleteSupplies": {"post": {"summary": "/api/supplies/deleteSupplies", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": []}}, "/api/invoices/getInvoices": {"post": {"summary": "/api/invoices/getInvoices", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Invoice"]}}, "/api/invoices/getInvoice": {"post": {"summary": "/api/invoices/getInvoice", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Invoice"]}}, "/api/invoices/createOrUpdateInvoice": {"post": {"summary": "/api/invoices/createOrUpdateInvoice", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Invoice"]}}, "/api/invoices/addUpdateItem": {"post": {"summary": "/api/invoices/addUpdateItem", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Invoice"]}}, "/api/invoices/deleteItem": {"post": {"summary": "/api/invoices/deleteItem", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Invoice"]}}, "/api/drugs/getDrugFamilies": {"post": {"summary": "/api/drugs/getDrugFamilies", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Drug"]}}, "/api/drugs/createOrEditDrugFamily": {"post": {"summary": "/api/drugs/createOrEditDrugFamily", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Drug", "DrugFamily"]}}, "/api/drugs/getDrugs": {"post": {"summary": "/api/drugs/getDrugs", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Drug"]}}, "/api/drugs/createOrEditDrug": {"post": {"summary": "/api/drugs/createOrEditDrug", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Drug"]}}, "/api/notifications/getNotifications": {"post": {"summary": "/api/notifications/getNotifications", "consumes": ["application/json"], "parameters": [], "responses": {"200": {"description": "OK", "schema": {"type": "object", "properties": {"docs": {"type": "array", "items": {}, "example": [null]}, "total": {"type": "number", "example": 0}, "limit": {"type": "number", "example": 7}, "page": {"type": "number", "example": 1}, "pages": {"type": "number", "example": 1}}}}}, "tags": ["Notification"], "produces": ["application/json"]}}, "/api/notifications/seenNotification": {"post": {"summary": "/api/notifications/seenNotification", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Notification"]}}, "/api/notifications/unseenNotificationNumber": {"post": {"summary": "/api/notifications/unseenNotificationNumber", "consumes": ["application/json"], "parameters": [], "responses": {"200": {"description": "OK", "schema": {"type": "number"}}}, "tags": ["Notification"], "produces": ["application/json"]}}, "/api/graphs/sessionTypesPie": {"post": {"summary": "/api/graphs/sessionTypesPie", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Session"]}}, "/api/graphs/sessionTypesLines": {"post": {"summary": "/api/graphs/sessionTypesLines", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Session"]}}, "/api/graphs/waitingTimeBars": {"post": {"summary": "/api/graphs/waitingTimeBars", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": []}}, "/api/graphs/dayOfWeeksSessions": {"post": {"summary": "/api/graphs/dayOfWeeksSessions", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Session"]}}, "/api/graphs/workedHours": {"post": {"summary": "/api/graphs/workedHours", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": []}}, "/api/graphs/ratesData": {"post": {"summary": "/api/graphs/ratesData", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": []}}, "/api/radios/getRadios": {"post": {"summary": "/api/radios/getRadios", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": []}}, "/api/radios/createOrEditRadio": {"post": {"summary": "/api/radios/createOrEditRadio", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": []}}, "/api/radios/getRadioFamilies": {"post": {"summary": "/api/radios/getRadioFamilies", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": []}}, "/api/radios/createOrEditRadioFamily": {"post": {"summary": "/api/radios/createOrEditRadioFamily", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": []}}, "/api/bios/getBios": {"post": {"summary": "/api/bios/getBios", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": []}}, "/api/bios/createOrEditBio": {"post": {"summary": "/api/bios/createOrEditBio", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": []}}, "/api/subscriptions/getPacks": {"post": {"summary": "/api/subscriptions/getPacks", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Pack"]}}, "/api/subscriptions/populatePacks": {"post": {"summary": "/api/subscriptions/populatePacks", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Pack"]}}, "/api/tenants/manageTenant": {"post": {"summary": "/api/tenants/manageTenant", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Tenant"]}}, "/api/tenants/getTenants": {"post": {"summary": "/api/tenants/getTenants", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Tenant"]}}, "/api/tenants/editTenant": {"post": {"summary": "/api/tenants/editTenant", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Tenant"]}}, "/api/tenants/delete": {"post": {"summary": "/api/tenants/delete", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Tenant"]}}, "/api/tenants/getProducts": {"post": {"summary": "/api/tenants/getProducts", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Tenant"]}}, "/api/tenants/createOrEditProduct": {"post": {"summary": "/api/tenants/createOrEditProduct", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Tenant"]}}, "/api/tenants/deleteProduct": {"post": {"summary": "/api/tenants/deleteProduct", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": ["Tenant"]}}, "/api": {"get": {"summary": "/api", "consumes": ["application/json"], "parameters": [], "responses": {}, "tags": []}}}, "tags": [{"name": "User"}, {"name": "Staff"}, {"name": "Patient"}, {"name": "Supplier"}, {"name": "SuperAdmin"}, {"name": "Profile"}, {"name": "Appointment"}, {"name": "Hospital"}, {"name": "Session"}, {"name": "Specialty"}, {"name": "Room"}, {"name": "Supply"}, {"name": "City"}, {"name": "Country"}, {"name": "Invoice"}, {"name": "Notification"}, {"name": "Prescription"}, {"name": "Diagnose"}, {"name": "Timeoff"}, {"name": "Drug"}, {"name": "DrugFamily"}, {"name": "RadiographFamily"}, {"name": "Radiograph"}, {"name": "Biologie"}, {"name": "Pack"}, {"name": "Feature"}, {"name": "TenantSubscription"}, {"name": "Tenant"}], "definitions": {"User": {"title": "User", "required": ["levelOfAccess"], "properties": {"email": {"type": "string", "required": false}, "password": {"type": "string"}, "levelOfAccess": {"type": "number"}, "active": {"type": "boolean"}, "alreadyConnected": {"type": "boolean"}, "profile": {"type": "string"}, "deletedAt": {"type": "string", "format": "date-time"}, "lastTimeConnected": {"type": "string", "format": "date-time"}, "totalConnections": {"type": "number"}, "FCMRegToken": {"type": "array", "items": {"type": "string"}}, "expiresIn": {"type": "number"}, "_id": {"type": "string"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}}}, "Staff": {"title": "Staff", "properties": {"title": {"type": "string", "enum": ["DOCTOR", "RECEPTIONIST"]}, "position": {"type": "string", "enum": ["SURGEON", "REGULAR_DOCTOR", "REGULAR_NURSE", "REGULAR_RECEPTIONIST"]}, "isAdmin": {"type": "boolean", "enum": false}, "hospital": {"type": "string"}, "specialty": {"type": "string"}, "deletedAt": {"type": "string", "format": "date-time"}, "updatedBy": {"type": "string"}, "createdBy": {"type": "string"}, "doctors": {"type": "array", "items": {"type": "string"}}, "receptionits": {"type": "array", "items": {"type": "string"}}, "residency": {"type": "string", "enum": ["TITULAR", "TEMPORARY"]}, "seniority": {"type": "string", "enum": ["SENIOR", "JUNIOR", "INTERN"]}, "profile": {"type": "string"}, "_id": {"type": "string"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}}}, "Patient": {"title": "Patient", "properties": {"hospital": {"type": "string"}, "deletedAt": {"type": "string", "format": "date-time"}, "updatedBy": {"type": "string"}, "createdBy": {"type": "string"}, "allergies": {"type": "array", "items": {"type": "string"}}, "chronicDiseases": {"type": "array", "items": {"type": "string"}}, "permanentDrugs": {"type": "array", "items": {"type": "string"}}, "insurance": {"type": "string"}, "insuranceId": {"type": "string"}, "sessionCounts": {"type": "number"}, "profile": {"type": "string"}, "_id": {"type": "string"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}}}, "Supplier": {"title": "Supplier", "properties": {"hospital": {"type": "string"}, "deletedAt": {"type": "string", "format": "date-time"}, "updatedBy": {"type": "string"}, "createdBy": {"type": "string"}, "profile": {"type": "string"}, "_id": {"type": "string"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}}}, "SuperAdmin": {"title": "SuperAdmin", "properties": {"hospital": {"type": "string"}, "deletedAt": {"type": "string", "format": "date-time"}, "updatedBy": {"type": "string"}, "createdBy": {"type": "string"}, "profile": {"type": "string"}, "_id": {"type": "string"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}}}, "Profile": {"title": "Profile", "properties": {"assignedID": {"type": "string"}, "title": {"type": "string", "enum": ["SUPER_ADMIN", "DOCTOR", "NURSE", "RECEPTIONIST", "PATIENT", "SUPPLIER"]}, "gender": {"type": "string", "enum": ["MALE", "FEMALE"]}, "phoneNumber": {"type": "string"}, "email": {"type": "string"}, "adress": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "startingDate": {"type": "string", "format": "date-time"}, "birthDate": {"type": "string", "format": "date-time"}, "hospital": {"type": "string"}, "profilePic": {"type": "string"}, "deletedAt": {"type": "string", "format": "date-time"}, "updatedBy": {"type": "string"}, "createdBy": {"type": "string"}, "language": {"type": "string"}, "city": {"type": "string"}, "country": {"type": "string"}, "username": {"type": "string"}, "address2": {"type": "string"}, "address3": {"type": "string"}, "staff": {"type": "string"}, "patient": {"type": "string"}, "supplier": {"type": "string"}, "superAdmin": {"type": "string"}, "height": {"type": "number"}, "weight": {"type": "number"}, "_id": {"type": "string"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}}}, "Appointment": {"title": "Appointment", "properties": {"hospital": {"type": "string"}, "patient": {"type": "string"}, "doctor": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string"}, "date": {"type": "string", "format": "date-time"}, "startTime": {"type": "string", "format": "date-time"}, "endTime": {"type": "string", "format": "date-time"}, "patientArrived": {"type": "string", "format": "date-time"}, "state": {"type": "string", "enum": ["PENDING", "APPROVED", "INPROGRESS", "CANCELED", "COMPLETED", "ALMOST_COMPLETED"]}, "updatedBy": {"type": "string"}, "createdBy": {"type": "string"}, "fromSession": {"type": "string"}, "supply": {"type": "string"}, "waitingTime": {"type": "number"}, "docs": {"type": "array", "items": {"type": "object", "properties": {"title": {"type": "string"}, "link": {"type": "string"}}, "required": []}}, "files": {"type": "array", "items": {"type": "object", "properties": {"title": {"type": "string"}, "link": {"type": "string"}}, "required": []}}, "stateOrder": {"type": "number"}, "certificate": {"type": "object", "properties": {"date": {"type": "string", "format": "date-time"}, "fromDate": {"type": "string", "format": "date-time"}, "toDate": {"type": "string", "format": "date-time"}, "releasedFrom": {"type": "string"}, "reason": {"type": "string"}}}, "_id": {"type": "string"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}}}, "Hospital": {"title": "Hospital", "properties": {"name": {"type": "string"}, "address": {"type": "string"}, "phoneNumbers": {"type": "array", "items": {"type": "string"}}, "localPhone": {"type": "string"}, "fax": {"type": "string"}, "phoneNumber": {"type": "string"}, "type": {"type": "string"}, "schedules": {"type": "array", "items": {"type": "object", "properties": {"day": {"type": "number"}, "startTime": {"type": "string"}, "endTime": {"type": "string"}, "startBreak": {"type": "string"}, "endBreak": {"type": "string"}}, "required": []}}, "sessions": {"type": "array", "items": {"type": "string"}}, "startTime": {"type": "string", "format": "date-time"}, "startBreak": {"type": "string", "format": "date-time"}, "endBreak": {"type": "string", "format": "date-time"}, "endTime": {"type": "string", "format": "date-time"}, "doctors": {"type": "array", "items": {"type": "string"}}, "receptionists": {"type": "array", "items": {"type": "string"}}, "avgSessionDuration": {"type": "number"}, "language": {"type": "string", "enum": ["fr", "en", "ar"]}, "currency": {"type": "string"}, "country": {"type": "string"}, "isManager": {"type": "boolean", "enum": false}, "code": {"type": "string"}, "city": {"type": "string"}, "address2": {"type": "string"}, "address3": {"type": "string"}, "email": {"type": "string"}, "tenant": {"type": "string"}, "prescriptionHeader": {"type": "boolean"}, "deletedAt": {"type": "string", "format": "date-time"}, "_id": {"type": "string"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}}}, "Session": {"title": "Session", "required": ["appointment"], "properties": {"hospital": {"type": "string"}, "appointment": {"type": "string"}, "patient": {"type": "string"}, "doctor": {"type": "string"}, "room": {"type": "string"}, "diagnoses": {"type": "array", "items": {"type": "string"}}, "date": {"type": "string", "format": "date-time"}, "startTime": {"type": "string", "format": "date-time"}, "endTime": {"type": "string", "format": "date-time"}, "title": {"type": "string"}, "type": {"type": "string"}, "notes": {"type": "array", "items": {"type": "object", "properties": {"title": {"type": "string"}, "link": {"type": "string"}, "noteType": {"type": "string"}}, "required": []}}, "docs": {"type": "array", "items": {"type": "object", "properties": {"title": {"type": "string"}, "link": {"type": "string"}}, "required": []}}, "updatedBy": {"type": "string"}, "createdBy": {"type": "string"}, "supply": {"type": "string"}, "prescriptions": {"type": "array", "items": {"type": "string"}}, "allergies": {"type": "array", "items": {"type": "string"}}, "chronicDiseases": {"type": "array", "items": {"type": "string"}}, "permanentDrugs": {"type": "array", "items": {"type": "string"}}, "height": {"type": "number"}, "weight": {"type": "number"}, "_id": {"type": "string"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}}}, "Specialty": {"title": "Specialty", "required": ["hospital"], "properties": {"hospital": {"type": "string"}, "name": {"type": "string"}, "content": {"type": "string"}, "priority": {"type": "number"}, "practicesConfiguration": {"type": "array", "items": {"type": "object", "properties": {}, "required": []}}, "practices": {"type": "array", "items": {"type": "object", "properties": {}, "required": []}}, "createdBy": {"type": "string"}, "updatedBy": {"type": "string"}, "_id": {"type": "string"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}}}, "Room": {"title": "Room", "required": ["hospital", "roomNumber"], "properties": {"hospital": {"type": "string"}, "roomNumber": {"type": "string"}, "standards": {"type": "string"}, "type": {"type": "string", "enum": ["EXTRACLINIQUE", "CONSULTATION", "BLOC"]}, "createdBy": {"type": "string"}, "updatedBy": {"type": "string"}, "_id": {"type": "string"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}}}, "Supply": {"title": "Supply", "required": ["name"], "properties": {"hospital": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "quantity": {"type": "number"}, "consumedNumber": {"type": "number"}, "costPrice": {"type": "number"}, "sellingPrice": {"type": "number"}, "avgDuration": {"type": "number"}, "type": {"type": "string", "enum": ["MATERIAL", "DRUG", "SESSION", "RADIOLOGIE", "BIOLOGIE"]}, "updatedBy": {"type": "string"}, "createdBy": {"type": "string"}, "supplier": {"type": "string"}, "otherSuppliers": {"type": "array", "items": {"type": "string"}}, "deletedAt": {"type": "string", "format": "date-time"}, "_id": {"type": "string"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}}}, "City": {"title": "City", "properties": {"name": {"type": "string"}, "country": {"type": "string"}, "deletedAt": {"type": "string", "format": "date-time"}, "_id": {"type": "string"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}}}, "Country": {"title": "Country", "properties": {"name": {"type": "string"}, "deletedAt": {"type": "string", "format": "date-time"}, "_id": {"type": "string"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}}}, "Invoice": {"title": "Invoice", "properties": {"hospital": {"type": "string"}, "session": {"type": "string"}, "appointment": {"type": "string"}, "buyer": {"type": "string"}, "seller": {"type": "string"}, "items": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "price": {"type": "number"}, "quantity": {"type": "number"}, "tax": {"type": "number"}, "supply": {"type": "string"}}, "required": []}}, "billingDate": {"type": "string", "format": "date-time"}, "paymentDate": {"type": "string", "format": "date-time"}, "total": {"type": "number"}, "paid": {"type": "number"}, "closed": {"type": "boolean"}, "updatedBy": {"type": "string"}, "createdBy": {"type": "string"}, "billingInformations": {"type": "number"}, "_id": {"type": "string"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}}}, "Notification": {"title": "Notification", "required": ["title"], "properties": {"hospital": {"type": "string"}, "title": {"type": "string"}, "content": {"type": "string"}, "profile": {"type": "string"}, "receiver": {"type": "string"}, "type": {"type": "string"}, "seenDate": {"type": "string", "format": "date-time"}, "_id": {"type": "string"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}}}, "Prescription": {"title": "Prescription", "properties": {"hospital": {"type": "string"}, "type": {"type": "string", "enum": ["ORDONNANCE", "BIOLOGIE", "RADIOLOGIE", "AUTRE"]}, "title": {"type": "string"}, "items": {"type": "array", "items": {"type": "object", "properties": {"notes": {"type": "array", "items": {"type": "string"}}, "name": {"type": "string"}, "drug": {"type": "string"}, "drugFamily": {"type": "string"}, "drugFamilyName": {"type": "string"}, "price": {"type": "number"}, "quantity": {"type": "number"}, "biologie": {"type": "string"}, "radiograph": {"type": "string"}, "radiographFamily": {"type": "string"}, "radiographFamilyName": {"type": "string"}}, "required": []}}, "session": {"type": "string"}, "updatedBy": {"type": "string"}, "createdBy": {"type": "string"}, "_id": {"type": "string"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}}}, "Diagnose": {"title": "Diagnose", "properties": {"hospital": {"type": "string"}, "specialty": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "severity": {"type": "string", "enum": ["", "EASY", "MEDIUM", "HARD"]}, "acts": {"type": "array", "items": {"type": "string"}}, "symptoms": {"type": "array", "items": {"type": "string"}}, "updatedBy": {"type": "string"}, "createdBy": {"type": "string"}, "isContagious": {"type": "boolean"}, "patients": {"type": "number"}, "sessions": {"type": "number"}, "_id": {"type": "string"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}}}, "Timeoff": {"title": "Timeoff", "properties": {"hospital": {"type": "string"}, "doctor": {"type": "string"}, "type": {"type": "string", "enum": ["INHOSPITAL", "HOMESESSION", "BREAK", "DAILY_BREAK"]}, "description": {"type": "string"}, "date": {"type": "string", "format": "date-time"}, "startTime": {"type": "string", "format": "date-time"}, "endTime": {"type": "string", "format": "date-time"}, "updatedBy": {"type": "string"}, "createdBy": {"type": "string"}, "isActive": {"type": "boolean"}, "isDaily": {"type": "boolean"}, "isWeekly": {"type": "boolean"}, "day": {"type": "number"}, "createdFrom": {"type": "string"}, "deleted": {"type": "boolean"}, "_id": {"type": "string"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}}}, "Drug": {"title": "Drug", "required": ["name"], "properties": {"hospital": {"type": "string"}, "name": {"type": "string"}, "drugFamily": {"type": "string"}, "price": {"type": "number"}, "updatedBy": {"type": "string"}, "createdBy": {"type": "string"}, "_id": {"type": "string"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}}}, "DrugFamily": {"title": "DrugFamily", "required": ["name"], "properties": {"hospital": {"type": "string"}, "name": {"type": "string"}, "updatedBy": {"type": "string"}, "createdBy": {"type": "string"}, "_id": {"type": "string"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}}}, "RadiographFamily": {"title": "RadiographFamily", "required": ["name"], "properties": {"hospital": {"type": "string"}, "name": {"type": "string"}, "updatedBy": {"type": "string"}, "createdBy": {"type": "string"}, "_id": {"type": "string"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}}}, "Radiograph": {"title": "Radiograph", "required": ["name"], "properties": {"hospital": {"type": "string"}, "name": {"type": "string"}, "radiographFamily": {"type": "string"}, "updatedBy": {"type": "string"}, "createdBy": {"type": "string"}, "_id": {"type": "string"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}}}, "Biologie": {"title": "Biologie", "required": ["name"], "properties": {"hospital": {"type": "string"}, "name": {"type": "string"}, "updatedBy": {"type": "string"}, "createdBy": {"type": "string"}, "_id": {"type": "string"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}}}, "Pack": {"title": "Pack", "properties": {"name": {"type": "string"}, "VAT_rate": {"type": "number"}, "annual_price_ttc": {"type": "number"}, "semester_price_ttc": {"type": "number"}, "quarterly_price_ttc": {"type": "number"}, "monthly_price_ttc": {"type": "number"}, "features": {"type": "array", "items": {"type": "string"}}, "deletedAt": {"type": "string", "format": "date-time"}, "_id": {"type": "string"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}}}, "Feature": {"title": "Feature", "properties": {"name": {"type": "string"}, "code": {"type": "string"}, "deletedAt": {"type": "string", "format": "date-time"}, "_id": {"type": "string"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}}}, "TenantSubscription": {"title": "TenantSubscription", "properties": {"tenant": {"type": "string"}, "periodicity": {"type": "string", "enum": ["M", "A", "S", "T"]}, "actif": {"type": "boolean"}, "terminated": {"type": "boolean"}, "startDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time"}, "numberOfUsers": {"type": "number"}, "comment": {"type": "string"}, "packs": {"type": "array", "items": {"type": "string"}}, "deletedAt": {"type": "string", "format": "date-time"}, "_id": {"type": "string"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}}}, "Tenant": {"title": "Tenant", "properties": {"name": {"type": "string"}, "subscription": {"type": "string"}, "code": {"type": "string"}, "hospital": {"type": "string"}, "actif": {"type": "boolean"}, "deletedAt": {"type": "string", "format": "date-time"}, "_id": {"type": "string"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}}}}, "info": {"title": "winmed-backend", "version": "0.1.12", "license": {"name": "ISC"}, "description": "Specification JSONs: [v2](/api-spec/v2), [v3](/api-spec/v3)."}}