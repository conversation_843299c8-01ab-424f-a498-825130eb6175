import UserController from '../controllers/UserController';
import { IS_LOGGED_IN , CHANGE_PASSWORD_VALIDATOR } from '../middlewares/authenticators';

export default (createEndpoint => {
  createEndpoint({
    method: 'post',
    path: '/signup',
    controller: UserController.signUp
  });
  createEndpoint({
    method: 'post',
    path: '/signin',
    controller: UserController.signIn
  });
  createEndpoint({
    method: 'post',
    path: '/signout',
    verify: [IS_LOGGED_IN],
    controller: UserController.signOut
  });
  createEndpoint({
    method: 'put',
    path: '/edituser',
    verify: [IS_LOGGED_IN],
    controller: UserController.editUser
  });
  createEndpoint({
    method: 'post',
    path: '/resetpassword',
    controller: UserController.resetPassword
  });
  createEndpoint({
    method: 'post',
    path: '/changepassword',
    verify: [CHANGE_PASSWORD_VALIDATOR],
    controller: UserController.changePassword
  });
  createEndpoint({
    method: 'post',
    path: '/isemailtaken',
    controller: UserController.isEmailTaken
  });
  createEndpoint({
    method: 'post',
    path: '/refreshUserStorage',
    controller: UserController.refreshUserStorage
  });
  createEndpoint({
    method: 'get',
    path: '/',
    controller: UserController.test
  });
});