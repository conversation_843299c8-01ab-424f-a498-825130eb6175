import Controller from './Controller';
import DrugService from "../services/DrugService";
import Drug from "../models/Drug";
const drugService = new DrugService(Drug);

class DrugController extends Controller {

  constructor(service) {
    super(service);
    this.getDrugs = this.getDrugs.bind(this);
    this.createOrEditDrug = this.createOrEditDrug.bind(this);
    this.getDrugFamilies = this.getDrugFamilies.bind(this);
    this.createOrEditDrugFamily = this.createOrEditDrugFamily.bind(this);
  }

  async getDrugs(req) {
    return drugService.getDrugs(req.body , req.user);
  }
  async createOrEditDrug(req) {
    return drugService.createOrEditDrug(req.body, req.user);
  }

  async getDrugFamilies(req) {
    return drugService.getDrugFamilies(req.body , req.user);
  }
  async createOrEditDrugFamily(req) {
    return drugService.createOrEditDrugFamily(req.body , req.user);
  }

}

export default new DrugController(drugService);