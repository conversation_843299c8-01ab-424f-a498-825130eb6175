import Service from './Service';
import APIError from '../errors/APIError';
import mongoose from 'mongoose';
import ProfileService from "../services/ProfileService";
import Profile from "../models/Profile";
const profileService = new ProfileService(Profile);
import moment from 'moment';
import Appointment from "../models/Appointment";
import { getUtcDate } from "../helpers/dates";
import socket from "../../config/socket";
import InvoiceService from "../services/InvoiceService";
import Invoice from "../models/Invoice";
import Notification from "../models/Notification";
import Prescription from "../models/Prescription"
const invoiceService = new InvoiceService(Invoice);
import {restructureProfileObj} from "../helpers/profileRework";
import {paginate} from "../helpers/dataOutput";
import Supply from "../models/Supply";


class SessionService extends Service {
    constructor(model) {
        super(model);
        this.getSessions = this.getSessions.bind(this);
        this.querySession = this.querySession.bind(this);
        this.updateSession=this.updateSession.bind(this);
        this.AvgWaitingTime = this.AvgWaitingTime.bind(this);
        this.createSession = this.createSession.bind(this);
        this.detailSession = this.detailSession.bind(this);
        this.createOrEditNote=this.createOrEditNote.bind(this);
        this.deleteNote=this.deleteNote.bind(this);
        this.createOrEditDrug=this.createOrEditDrug.bind(this);
        this.deleteDrug=this.deleteDrug.bind(this);
        this.addDiagnose=this.addDiagnose.bind(this);
        this.deleteDiagnose=this.deleteDiagnose.bind(this);
        this.toAlmostCompleted=this.toAlmostCompleted.bind(this);
        this.averageTime=this.averageTime.bind(this);
        this.createOrEditPrescriptionPage=this.createOrEditPrescriptionPage.bind(this);
        this.deletePrescriptionPage=this.deletePrescriptionPage.bind(this);
        this.updateProfileFromSession=this.updateProfileFromSession.bind(this)
    }

    async querySession(filters, query = {}, user) {

        // date range
        if (filters.startDate || filters.endDate) {
            let dateQuery = {};
            if(filters.startDate) dateQuery["$gte"] = new Date(filters.startDate);
            if(filters.endDate) dateQuery["$lte"] = new Date(filters.endDate);
            query.date = dateQuery;
        }

        //date
        if (filters.date) {
            query.date = new Date(filters.date);
        }

        //startTime
        if (filters.startTime) {
            query.startTime = { $gte: new Date(filters.startTime) };
        }
        //endTime
        if (filters.endTime) {
            query.endTime = { $lte: new Date(filters.endTime) };
        }
        //type
        if (filters.room) {
            query.room = mongoose.Types.ObjectId(filters.room);
        }
        //type
        if (filters.appointment) {
            query.appointment = mongoose.Types.ObjectId(filters.appointment);
        }
        //patients
    if (filters.patient) {
        const profile = await Profile.findOne({_id : mongoose.Types.ObjectId(filters.patient)})
        if(profile) query.patient = profile.patient._id;
      }
        //doctor
        if (filters.doctor) {
            let doctorIDs = await profileService.findProfiles({ titles: ['DOCTOR'], searchText: filters.doctor }, 1, 1000, user);
            doctorIDs = Array.from(doctorIDs).map(x => x._doc.staffId);
            if (filters.doctor.length == 12){
                const profile = await Profile.findOne({_id : mongoose.Types.ObjectId(filters.doctor)})
                doctorIDs.push(profile.staff._id);
            }
            query.doctor = { $in: doctorIDs };
        }
        //sessionsIDs
        if (filters.sessionIDs) {
            query._id = { $in: filters.sessionIDs };
        }
        return query;
    }
    async getSessions(filters, page, limit, user) {
        let query = { hospital: mongoose.Types.ObjectId(user.profile.hospital._id) };
        query = await this.querySession(filters, query, user);
        limit = parseInt(limit, 10) || 1000; 
        page = parseInt(page, 10) || 1;
        let sessions = await this.model.find(query).sort({ date: -1, startTime: -1, endTime: -1, type: 1 });
        if (!sessions) throw new APIError(404, 'cannot find sessions');
        if(filters.states) sessions = sessions.filter(s => filters.states.includes(s.appointment.state));
        sessions = paginate(sessions,limit,page);
        sessions.docs = sessions.docs.map(s => {
            const sessionObj = Object.assign({}, s._doc);
            const appointmentObj = Object.assign({}, s.appointment._doc);
            sessionObj.doctor = restructureProfileObj(s.doctor  , false);
            sessionObj.patient = restructureProfileObj(s.patient , false);
            appointmentObj.doctor = restructureProfileObj(s.appointment.doctor  , false);
            appointmentObj.patient = restructureProfileObj(s.appointment.patient , false);
            sessionObj.appointment = appointmentObj;
            return sessionObj;
        })
        return sessions;
    }

    AvgWaitingTime(sessions) {
        let AvgWaitingTime = 0;
        let filteredSessions=sessions.filter(x => x.appointment.state == 'COMPLETED' || x.appointment.state == 'INPROGRESS' || x.appointment.state == 'ALMOST_COMPLETED');
        filteredSessions.map(x => {
            if (new Date(x.startTime).getTime() > new Date(x.appointment.startTime).getTime()) AvgWaitingTime = AvgWaitingTime + x.appointment.waitingTime;
        });


        AvgWaitingTime = AvgWaitingTime / filteredSessions.length;
        return AvgWaitingTime;
    }
    async createSession(session = {}, appointment, user) {
        session.hospital = user.profile.hospital._id;
        session.createdBy = user.profile._id;
        session.updatedBy = user.profile._id;
        if (!session.appointment) session.appointment = appointment._id;
        if (!session.patient) session.patient = appointment.patient._id || appointment.patient;
        if (!session.doctor) session.doctor = appointment.doctor._id || appointment.doctor;
        if (!session.date) session.date = appointment.date;
        if (!session.type) session.type = appointment.type;
        if (!session.supply) session.supply = appointment.supply._id|| appointment.supply;
        if (!session.startTime) session.startTime = Date.now();
        if (!session.endTime) session.endTime = moment(new Date(session.startTime)).add(appointment.supply.avgDuration, 'minutes').toDate();

        if (appointment.fromSession) {
            session.diagnoses = appointment.fromSession.diagnoses.concat(session.diagnoses || []);
        }

        let query = { hospital: mongoose.Types.ObjectId(user.profile.hospital._id), appointment: mongoose.Types.ObjectId(appointment._id)};
        session = await this.model.findOneAndUpdate(query, session, { upsert: true, new: true, setDefaultsOnInsert: true });
        if (!session) throw new APIError(403, 'cannot create sessions');
        if(!appointment) appointment=await Appointment.findById(session.appointment._id).select('startTime patientArrived') ;
        if(!appointment.patientArrived) appointment.patientArrived=session.startTime;
        let waitingTime=(new Date(session.startTime).getTime()-new Date(appointment.patientArrived).getTime())/(1000*60);
        appointment=await Appointment.findByIdAndUpdate(appointment._id,{waitingTime:waitingTime},{new:true});
        session.appointment=appointment;
        //createInvoice
        let supplySession=await Supply.findOne({
            hospital: mongoose.Types.ObjectId(user.profile.hospital._id),
            _id:session.supply._id,
            type:'SESSION'
        });
        let items=[];
        if(supplySession) items.push({
            name: supplySession.name,
            price: 100,
            quantity: 1,
            tax: 0,
            supply: supplySession._id
        })
        let invoice={
            session:session._id,
            appointment:session.appointment._id,
            buyer:session.patient.profile._id,
            seller:session.doctor.profile._id,
            items:items,
            billingDate:new Date(session.date),
            total:supplySession.sellingPrice,
            paid:0
        }
        await invoiceService.createOrUpdateInvoice(invoice,null,user);
        return session;
    }

    async updateSession(sessionID, session, user,socketID) {
        session.updatedBy = user.profile._id;
        let query = { hospital: mongoose.Types.ObjectId(user.profile.hospital._id), _id: mongoose.Types.ObjectId(sessionID) };
        session = await this.model.findOneAndUpdate(query, session, { new: true });
        if (!session) throw new APIError(403, 'cannot update session');
        let appointment = Object.assign({} , session._doc);
        delete appointment._id;
        appointment=await Appointment.findOneAndUpdate({ hospital: mongoose.Types.ObjectId(user.profile.hospital._id),_id:mongoose.Types.ObjectId(session.appointment._id)}, appointment, { new: true });

        session._doc.doctor =  restructureProfileObj(session.doctor ,  false);
        session._doc.patient =  restructureProfileObj(session.patient ,  false);
        appointment._doc.doctor =  restructureProfileObj(session.appointment.doctor ,  false);
        appointment._doc.patient =  restructureProfileObj(session.appointment.patient ,  false);

        session.appointment = appointment

        socket.socket.emitToRoom(user.profile.hospital._id,session._id+"",{profile:user.profile,session,operation:'update',socketID});
        return session;
    }

    async detailSession(filters, user) {
        let query = { hospital: mongoose.Types.ObjectId(user.profile.hospital._id) };
        if (filters.sessionID) query._id = mongoose.Types.ObjectId(filters.sessionID);
        if (filters.doctorID){
            const doctorProfile = await Profile.findOne({_id: appointment.doctor});
            query.doctor = doctorProfile.staff._id;
        } 
        if (filters.date) query.date = new Date(date);
        if (filters.startTime) {
            query.startTime = { $gte: new Date(filters.startTime) };
        }
        if (filters.endTime) {
            query.endTime = { $lte: new Date(filters.endTime) };
        }
        if (filters.appointmentID) {
            query.appointment = mongoose.Types.ObjectId(filters.appointmentID);
        }
        if (filters.InProgress) {
            let appointments = await Appointment.find({
                state: "INPROGRESS",
                hospital: mongoose.Types.ObjectId(user.profile.hospital._id),
                doctor: mongoose.Types.ObjectId(user.profile.staff._id),
                date: getUtcDate(new Date())
            });
            if (appointments.length <= 0 ) return [];
            if (appointments){
                const appointmentIDs = appointments.map(a => a._id)
                query.appointment = {$in: appointmentIDs}
            }
        }
        let sessions = await this.model.find(query);
        if (sessions.length <= 0) throw new APIError(404, 'no sessions found');

        const sessionIDs = sessions.map(session => session._id);
        let invoices = await Invoice.find({session: {$in: sessionIDs},hospital:mongoose.Types.ObjectId(user.profile.hospital._id),});
        let futurAppointments =await Appointment.find({hospital:mongoose.Types.ObjectId(user.profile.hospital._id),fromSession:{$in: sessionIDs}});

        const sessionHistoryPromises = sessions.map(async session => {
            const states = ["COMPLETED"]
            let querySessionHistory = {
                date: { $lte: new Date(session.date) },
                hospital: mongoose.Types.ObjectId(user.profile.hospital._id),
                patient: mongoose.Types.ObjectId(session.patient._id),
            };
            let historySessions = await this.model.find(querySessionHistory).sort({ date: -1, startTime: -1, endTime: -1, type: 1 });
            historySessions = historySessions.filter(s => states.includes(s.appointment.state));
            historySessions = paginate(historySessions,3,1);
            return historySessions;
        })

        const sessionHistories = await Promise.all(sessionHistoryPromises);

        const fullSessions = sessions.map((session) => {
            const historySessions = sessionHistories.find(sh => {
                return sh.docs[0] && sh.docs[0].patient._id +""== session.patient._id +""
            })
            const invoice = invoices.find(invoice => invoice.session._id+"" == session._id+"");
            const futurAppointment = futurAppointments.find(futurAppointment => futurAppointment.fromSession._id+"" == session._id+"");
           
            const sessionObj= Object.assign({},session._doc)
            const sessionAppointment = Object.assign({},session.appointment._doc);
            sessionObj.doctor =  restructureProfileObj(session.doctor ,  false);
            sessionObj.patient =  restructureProfileObj(session.patient ,  false);
            sessionAppointment.doctor =  restructureProfileObj(session.appointment.doctor ,  false);
            sessionAppointment.patient =  restructureProfileObj(session.appointment.patient ,  false);
            sessionObj.appointment = sessionAppointment
            session = sessionObj
            
            if(futurAppointment){
                futurAppointment._doc.doctor =  restructureProfileObj(futurAppointment.doctor , false);
                futurAppointment._doc.patient =  restructureProfileObj(futurAppointment.patient , false);
            }

            if(invoice){
                const invoiceSession = Object.assign({},invoice.session._doc)
                const appointmentSessionInvoice = Object.assign({},invoice.session.appointment._doc);
                appointmentSessionInvoice.doctor =  restructureProfileObj(invoice.session.appointment.doctor ,  false);
                appointmentSessionInvoice.patient =  restructureProfileObj(invoice.session.appointment.patient ,  false);
                invoice._doc.seller = restructureProfileObj(invoice.seller , true);
                invoice._doc.buyer = restructureProfileObj(invoice.buyer  , true);
                invoiceSession.doctor = restructureProfileObj(invoice.session.doctor  , false);
                invoiceSession.patient = restructureProfileObj(invoice.session.patient , false);
                invoiceSession.appointment = appointmentSessionInvoice
                invoice._doc.session = invoiceSession
            }

            if(historySessions){
                historySessions.docs = historySessions.docs.map(s => {
                    s._doc.doctor =  restructureProfileObj(s.doctor ,  false);
                    s._doc.patient =  restructureProfileObj(s.patient ,  false);
                    return s
                })
            }
            
        return { futurAppointment,session , historySessions ,invoice};

        })
        
        return fullSessions;
    }


    async createOrEditNote(note, sessionID, user,socketID) {
        let query={_id:mongoose.Types.ObjectId(sessionID),
            hospital:mongoose.Types.ObjectId(user.profile.hospital._id)
        };
        let session=await this.model.findOne(query);
        if (!session) throw new APIError(403, 'cannot find session');
        session.updatedBy=user.profile._id;
        if(!session.notes) session.notes=[];
        let indexNote=session.notes.findIndex(x=>x._id+''==note._id);
        if(indexNote<0) {
            indexNote=session.notes.length;
            session.notes.push(note)
        }
        else session.notes[indexNote]=note;
        session=await session.save();
        if (!session) throw new APIError(403, 'cannot update session');
        socket.socket.emitToRoom(user.profile.hospital._id,session._id+"",{profile:user.profile,session,operation:'update',socketID});

        return session.notes[indexNote];
    }
    async deleteNote(noteID, sessionID, user,socketID) {
        let query={_id:mongoose.Types.ObjectId(sessionID),
            hospital:mongoose.Types.ObjectId(user.profile.hospital._id)
        };
        let session=await this.model.findOneAndUpdate(query,{$set:{updatedBy:user.profile._id},$pull:{'notes':{'_id':mongoose.Types.ObjectId(noteID)}}},{new:true});
        if (!session) throw new APIError(403, 'cannot update session');
        socket.socket.emitToRoom(user.profile.hospital._id,session._id+"",{profile:user.profile,session,operation:'update',socketID});

        return session;
        
    }
    async createOrEditDrug(drug, sessionID, user,socketID) {
        let query={_id:mongoose.Types.ObjectId(sessionID),
            hospital:mongoose.Types.ObjectId(user.profile.hospital._id)
        };
        let session=await this.model.findOne(query);
        if (!session) throw new APIError(403, 'cannot find session');
        session.updatedBy=user.profile._id;
        if(!session.prescription) session.prescription=[];
        let indexNote=session.prescription.findIndex(x=>x._id+''==drug._id);
        if(indexNote<0) {
            indexNote=session.prescription.length;
            session.prescription.push(drug);
        }
        else session.prescription[indexNote]=drug;
        session=await session.save();
        if (!session) throw new APIError(403, 'cannot update session');
        socket.socket.emitToRoom(user.profile.hospital._id,session._id+"",{profile:user.profile,session,operation:'update',socketID});
        return session.prescription[indexNote];
    }
    async deleteDrug(drugID, sessionID, user,socketID) {
        let query={_id:mongoose.Types.ObjectId(sessionID),
            hospital:mongoose.Types.ObjectId(user.profile.hospital._id),
        };
        let session=await this.model.findOneAndUpdate(query,{$set:{updatedBy:user.profile._id},$pull:{'prescription':{'_id':mongoose.Types.ObjectId(drugID)}}},{new:true});
        if (!session) throw new APIError(403, 'cannot update session');
        socket.socket.emitToRoom(user.profile.hospital._id,session._id+"",{profile:user.profile,session,operation:'update',socketID});

        return session;  
    }
    async addDiagnose(diagnoseID, sessionID, user,socketID) {
        let query={_id:mongoose.Types.ObjectId(sessionID),
            hospital:mongoose.Types.ObjectId(user.profile.hospital._id)
        };
        let session=await this.model.findOneAndUpdate(
            query,
            {
                $set:{updatedBy:user.profile._id}
                ,$push:{
                diagnoses:mongoose.Types.ObjectId(diagnoseID)
        }},{new:true});
        if (!session) throw new APIError(403, 'cannot add diagnose');
        let diagnose=session.diagnoses.find(x=>x._id+""==diagnoseID+"");
        if (!diagnose) throw new APIError(403, 'cannot add diagnose');
        socket.socket.emitToRoom(user.profile.hospital._id,session._id+"",{profile:user.profile,session,operation:'update',socketID});

        return diagnose;
    }
    async deleteDiagnose(diagnoseID, sessionID, user,socketID) {
        let query={_id:mongoose.Types.ObjectId(sessionID),
            hospital:mongoose.Types.ObjectId(user.profile.hospital._id),
        };
        let session=await this.model.findOneAndUpdate(query,{$set:{updatedBy:user.profile._id},$pull:{
            diagnoses:mongoose.Types.ObjectId(diagnoseID)
        }},{new:true});
        if (!session) throw new APIError(403, 'cannot delete diagnose');
        socket.socket.emitToRoom(user.profile.hospital._id,session._id+"",{profile:user.profile,session,operation:'update',socketID});

        return session;
    }
    async toAlmostCompleted (appointmentID,user,socketID){
        let query={
            hospital:mongoose.Types.ObjectId(user.profile.hospital._id),
            _id:mongoose.Types.ObjectId(appointmentID),
            //state:"INPROGRESS"
          }

          let appointment = await Appointment.findOneAndUpdate(query,{state:"ALMOST_COMPLETED"},{new:true});
          if (!appointment) throw new APIError(403, 'cannot update appointment');
            appointment.patient._doc = restructureProfileObj(appointment.patient ,false)
            appointment.doctor._doc = restructureProfileObj(appointment.doctor ,false)
            
          socket.socket.emitToRoom(user.profile.hospital._id,appointment._id+"",{profile:user.profile,appointment,operation:'update',socketID});
          socket.socket.emitToRoom(user.profile.hospital._id,'header-update',{profile:user.profile});

          await this.model.findOneAndUpdate({
                hospital:mongoose.Types.ObjectId(user.profile.hospital._id),
                appointment:mongoose.Types.ObjectId(appointmentID),
            },{endTime:Date.now()},{new:true});

            const profile = await Profile.findOne({_id: user.profile._id});
            const staff =  await profile.staff.populate("receptionits").execPopulate();

            for(let i=0;i<staff.receptionits.length;i++){
                let notification=await new Notification({
                    profile:user.profile._id,
                    receiver:staff.receptionits[i]._id,
                    type:'APPOINTMENT',
                    hospital:user.profile.hospital._id,
                    title:"Rendez-vous terminé",
                    content:" vient de libérer son patient"
                }).save();
                socket.socket.emitToRoom(user.profile.hospital._id,notification.receiver+"-notification",{notification,profile:user.profile,socketID});
            }

          return appointment;
    }

    async averageTime(filters,user){
        let query={
            hospital:mongoose.Types.ObjectId(user.profile.hospital._id),
            doctor:{$in:user.profile.doctors}
          };
          if(filters.date)query.date=new Date(filters.date);
          let types=user.profile.hospital.sessions.map(x=>x.name);
        let sessions=await this.model.find(query).select('startTime endTime supply');
        
        let results=[];
        let totalWorkedHours=0,totalAvgSessionTime=0;
        types.map(type=>{
            let workedHours=0,numberOfSessions=0;
            sessions.filter(session=> session.supply && type==session.supply.name).map(x=>{
                if(!x.endTime){
                    x.endTime=moment(new Date(x.startTime)).add(x.supply.avgDuration, 'minutes').toDate();
                }

          if(x.endTime && x.startTime && new Date(x.endTime).getTime()> new Date(x.startTime).getTime()){
            totalWorkedHours=totalWorkedHours+((new Date(x.endTime).getTime()-new Date(x.startTime).getTime())/(60*1000));
;              numberOfSessions++;
             workedHours=workedHours+((new Date(x.endTime).getTime()-new Date(x.startTime).getTime())/(60*1000));
          }        
        })
        if(numberOfSessions>0) workedHours=workedHours/numberOfSessions;
        if(sessions.length>0) totalAvgSessionTime=totalWorkedHours/sessions.length;
        results.push({type:type,avgSessionTime:workedHours,workedHours:workedHours*numberOfSessions})
        })
        return {workedHours:totalWorkedHours,totalAvgSessionTime,avgSessionTimePerType:results}
        
    }

    async createOrEditPrescriptionPage(sessionID,type,page,user,socketID){
        let pageID = page._id;
        if(type) page.type = type;
        if(sessionID) page.session = sessionID;
        page.hospital=user.profile.hospital._id;
        page.updatedBy=user.profile._id;
        if(!pageID) page.createdBy=user.profile._id;
        let query={_id : mongoose.Types.ObjectId(pageID) , hospital:mongoose.Types.ObjectId(user.profile.hospital._id)};
        page = await Prescription.findOneAndUpdate(query,page,{new:true,upsert: true, setDefaultsOnInsert:true});
        if (!page) throw new APIError(404, 'cannot create / update Prescription page');
        if(sessionID){
            const sessionQuery = {_id : mongoose.Types.ObjectId(sessionID) , hospital:mongoose.Types.ObjectId(user.profile.hospital._id)}
            let session = await this.model.findOne(sessionQuery);
            if(session){
                session.prescriptions.addToSet(page._id)
                await session.save();
            }
        }
        return page;
    }

    async deletePrescriptionPage(sessionID,prescriptionPageId,user,socketID){
        const queryPrescriptionPage ={_id : mongoose.Types.ObjectId(prescriptionPageId) , hospital:mongoose.Types.ObjectId(user.profile.hospital._id)};
        const querySession ={_id : mongoose.Types.ObjectId(sessionID) , hospital:mongoose.Types.ObjectId(user.profile.hospital._id)};

        const prescriptionPage = await Prescription.deleteOne(queryPrescriptionPage);
        if (!prescriptionPage) throw new APIError(400, 'cannot delete Prescription page'); 
        const session  = await this.model.updateOne(querySession , {$pull:{prescriptions:prescriptionPageId}})
        return prescriptionPage
    }

    async updateProfileFromSession(sessionID, user){
        let profile = {};
        const session = await this.model.findOne({hospital: mongoose.Types.ObjectId(user.profile.hospital._id) , _id: mongoose.Types.ObjectId(sessionID)});
        if(session){
            let profileUpdate = {};
            if(session.weight) profileUpdate.weight = session.weight;
            if(session.height) profileUpdate.height = session.height; 
            if(session.patient && session.patient.profile && session.patient.profile._id ){
                profile = await Profile.findOneAndUpdate({hospital: mongoose.Types.ObjectId(user.profile.hospital._id) , _id: mongoose.Types.ObjectId(session.patient.profile._id)}, profileUpdate)
            }
        }
        return profile;
    }
}

export default SessionService;