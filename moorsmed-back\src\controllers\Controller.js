class Controller {
  constructor(service) {
    this.service = service;
    this.getAll = this.getAll.bind(this);
    this.insert = this.insert.bind(this);
    this.update = this.update.bind(this);
    this.delete = this.delete.bind(this);
    this.insertMany = this.insertMany.bind(this);
    this.updateMany = this.updateMany.bind(this);
    this.deleteMany = this.deleteMany.bind(this);
    this.findOne = this.findOne.bind(this);
    this.updateOne = this.updateOne.bind(this);
  }

  async getAll(req) {
    return this.service.getAll(req.query);
  }

  async insert(req) {
    return this.service.insert(req.body);
  }

  async update(req) {
    return this.service.update(req.params.id, req.body);
  }

  async delete(req) {
    return this.service.delete(req.params.id);
  }

  async insertMany(req) {
    return this.service.insertMany(req.body);
  }

  async updateMany(req) {
    return this.service.updateMany(req.body.filter, req.body.update);
  }

  async deleteMany(req) {
    return this.service.deleteMany(req.body.filter);
  }

  async findOne(req) {
    return this.service.findOne(req.params.id);
  }

  async updateOne(req) {
    return this.service.updateOne(req.body.filter, req.body.update);
  }
}

export default Controller;