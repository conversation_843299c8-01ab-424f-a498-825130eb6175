import AppointmentController from '../controllers/AppointmentController';
import { IS_LOGGED_IN, IS_ALLOWED } from '../middlewares/authenticators';

export default (createEndpoint => {
  createEndpoint({
    method: 'post',
    path: '/getAppointments',
    verify: [IS_LOGGED_IN],
    controller: AppointmentController.getAppointments
  });
  createEndpoint({
    method: 'post',
    path: '/createAppointment',
    verify: [IS_LOGGED_IN],
    controller: AppointmentController.createAppointment
  });
  createEndpoint({
    method: 'post',
    path: '/updateAppointment',
    verify: [IS_LOGGED_IN],
    controller: AppointmentController.updateAppointment
  });
  createEndpoint({
    method: 'post',
    path: '/updateMultipleAppointment',
    verify: [IS_LOGGED_IN],
    controller: AppointmentController.updateMultipleAppointment
  });
  createEndpoint({
    method: 'post',
    path: '/getIntervalAppointment',
    verify: [IS_LOGGED_IN],
    controller: AppointmentController.getIntervalAppointment
  });
  createEndpoint({
    method: 'post',
    path: '/doctorView',
    verify: [IS_LOGGED_IN, IS_ALLOWED(['DOCTOR'])],
    controller: AppointmentController.doctorView
  });
  createEndpoint({
    method: 'post',
    path: '/toCompleted',
    verify: [IS_LOGGED_IN],
    controller: AppointmentController.toCompleted
  });
  createEndpoint({
    method: 'post',
    path: '/waitingPatients',
    verify: [IS_LOGGED_IN],
    controller: AppointmentController.waitingPatients
  });
  createEndpoint({
    method: 'post',
    path: '/statesPerDay',
    verify: [IS_LOGGED_IN],
    controller: AppointmentController.statesPerDay
  });
  createEndpoint({
    method: 'post',
    path: '/switchAppointment',
    verify: [IS_LOGGED_IN],
    controller: AppointmentController.switchAppointment
  });
  createEndpoint({
    method: 'post',
    path: '/createUpdateTimeoff',
    verify: [IS_LOGGED_IN],
    controller: AppointmentController.createUpdateTimeoff
  });
  createEndpoint({
    method: 'post',
    path: '/deleteTimeoff',
    verify: [IS_LOGGED_IN],
    controller: AppointmentController.deleteTimeoff
  });
  
  createEndpoint({
    method: 'post',
    path: '/getTimeOffs',
    verify: [IS_LOGGED_IN],
    controller: AppointmentController.getTimeOffs
  });
  createEndpoint({
    method: 'post',
    path: '/timeProposition',
    verify: [IS_LOGGED_IN],
    controller: AppointmentController.timeProposition
  });
  createEndpoint({
    method: 'post',
    path: '/checkPlanning',
    verify: [IS_LOGGED_IN],
    controller: AppointmentController.checkPlanning
  });
  createEndpoint({
    method: 'post',
    path: '/checkBeforeTimeoff',
    verify: [IS_LOGGED_IN],
    controller: AppointmentController.checkBeforeTimeoff
  });
  createEndpoint({
    method: 'post',
    path: '/deleteTimeoffFix',
    verify: [IS_LOGGED_IN],
    controller: AppointmentController.deleteTimeoffFix
  });
  createEndpoint({
    method: 'post',
    path: '/doctorBadgeStat',
    verify: [IS_LOGGED_IN],
    controller: AppointmentController.doctorBadgeStat
  });
});