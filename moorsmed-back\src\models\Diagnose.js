import mongoose from "mongoose";
const Schema = mongoose.Schema;
import mongoosePaginate from 'mongoose-paginate';
import { DIAGNOSE_SEVERITY} from "../../config/utils/variables";

const DiagnoseSchema = new mongoose.Schema({
    hospital: {
        type: Schema.Types.ObjectId,
        ref: 'Hospital'
    },
    specialty: {
        type: Schema.Types.ObjectId,
        ref: 'Specialty'
    },
    name: {
        type: String
    },
    description: {
        type: String,
        default: ''
    },
    severity: {
        type: String,
        enum: DIAGNOSE_SEVERITY,
        uppercase: true,
        default: ""
    },
    acts: [String],
    symptoms: [String],
    updatedBy: {
        type: Schema.Types.ObjectId,
        ref: 'Profile'
    },
    createdBy: {
        type: Schema.Types.ObjectId,
        ref: 'Profile'
    },
    isContagious:{
        type:Boolean
        },
    patients:{type:Number,default:0},
    sessions:{type:Number,default:0},


}, {
    timestamps: true,
    collation: { locale: 'fr', strength: 1 }
});
DiagnoseSchema.plugin(mongoosePaginate);
DiagnoseSchema.pre('find', populateDiagnoses);
DiagnoseSchema.pre('findOne', populateDiagnoses);
DiagnoseSchema.pre('findOneAndUpdate', populateDiagnoses);
function populateDiagnoses(next) {
    this.populate('specialty', "name").populate('createdBy', "firstName lastName title").populate('updatedBy', "firstName lastName title").sort({ name: 1, createdBy: 1, updatedBy: 1 });

    next();
}

module.exports = mongoose.model("Diagnose", DiagnoseSchema);