import helmet from 'helmet';

const config = require('../config');

export default [
  helmet(), // adds headers that generically protect against simple attacks
  function cors<PERSON><PERSON><PERSON>(req, res, next) {
    const {origin} = req.headers;
    if (
      /* process.env.NODE_ENV !== 'production' ||
   config.allowedOrigins.indexOf(origin) > -1*/
      true
    ) {
      res.header('Access-Control-Allow-Origin', origin);
    } else {
      res.header('Access-Control-Allow-Credentials', 'true');
      res.header('Access-Control-Allow-Methods', 'GET,PUT,HEAD,OPTIONS,POST,DELETE');
      res.header(
        'Access-Control-Allow-Headers',
        'Access-Control-Allow-Headers, Origin,Accept, X-Requested-With, Content-Type, Access-Control-Request-Method, Access-Control-Request-Headers, Authorization, Cache-Control',
      );
      res.header('Access-Control-Max-Age', '600');
      next();
    }
  },
];
