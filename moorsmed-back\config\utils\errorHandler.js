export default [
// 404 handler
(req, res, next) => {
  const error = new Error('NOTFOUND');
  error.status = 404;
  next(error);
},
// error handler / printer
// maybe disable this on prod?
(error, req, res) => {
  if (error.name === 'JsonSchemaValidation') {
    error.status = 400;
    error.message = 'Invalid request body';
  }
  res.status(error.status || 500);
  res.json({
    error: true,
    statusCode: error.status || 500,
    message: error.message
  });
}];