require('dotenv').config({ path: process.env.NODE_ENV ? `environments/.env.${process.env.NODE_ENV}` : "environments/.env.dev" });

const { exec } = require('child_process');
const path = require('path');
const fs = require('fs');

if(!process.env.BACKUP_NAME) throw new Error("Backup name not specified")
const backupDirectory = path.join(process.env.DB_BACKUP_PATH, process.env.BACKUP_NAME, process.env.DATABASE_NAME);
if(!fs.existsSync(backupDirectory)) throw new Error("Folder not found");

// Execute mongorestore command
const command = `mongorestore --db ${process.env.DATABASE_NAME} --drop ${backupDirectory}`;

exec(command, (error, stdout, stderr) => {
  if (error) {
    console.error('Error during database restoration:', error.message);
    return;
  }
  if (stderr) {
    console.error('Error during database restoration:', stderr);
    return;
  }
  console.log('Database restoration successful!');
});