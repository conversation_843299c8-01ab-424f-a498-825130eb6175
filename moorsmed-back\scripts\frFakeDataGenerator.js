const dummy = require('mongoose-dummy');
import mongoose from 'mongoose';
import moment from 'moment';

import User from '../src/models/User';
import Profile from '../src/models/Profile';
import Hospital from '../src/models/Hospital';
import Room from '../src/models/Room';
import Specialty from '../src/models/Specialty';
import Appointment from '../src/models/Appointment';
import Session from '../src/models/Session';
import Diagnose from '../src/models/Diagnose';
import Supply from '../src/models/Supply';
import Invoice from '../src/models/Invoice';
import DiagnoseService from '../src/services/DiagnoseService';
const diagnoseService = new DiagnoseService(Diagnose);
import {getUtcDate, copyTime, compareDates, getDateFromTime} from '../src/helpers/dates';
import {APPOINTMENT_TYPES} from '../config/utils/variables';
import AppointmentService from '../src/services/AppointmentService';
const appointmentService = new AppointmentService(Appointment);

function rnd(min, max) {
  return parseInt(Math.random() * (max - min) + min, 10);
}
export default async (fDate, tDate) => {
  //hospital
  let hospitalsNumber = 1;
  let hospitalIDs = [];
  for (let h = 0; h < hospitalsNumber; h++) {
    let regularProfile = {
      assignedID: 'BH231425',
      phoneNumber: '+212656721923',
      adress: 'JAMILA 10, NR 8,CD CASA',
      insuranceId: '5214-2458',
      insurance: 'CNOPS',
      allergies: ["l'arachide", 'urticaire ou eczéma '],
      chronicDiseases: ['Diabète'],
      permanentDrugs: ['Cardio aspirine 100mg'],
      language: 'fr',
    };
    let regularAppointment = {
      files: [
        {
          title: 'Analyse laboratoire',
          link:
            'https://www.fichier-pdf.fr/2019/08/26/liste-des-analyses/preview-liste-des-analyses-1.jpg',
        },
      ],
      docs: [
        {
          title: 'Analyse laboratoire',
          link:
            'https://www.fichier-pdf.fr/2019/08/26/liste-des-analyses/preview-liste-des-analyses-1.jpg',
        },
      ],
    };
    let regularSession = {
      notes: [
        {
          title: 'A propos des analyses',
          link: "il va mieux, même s'il doit se reposer davantage",
        },
        {
          title: 'Après examination',
          link: 'bons battements de coeur, pression de hauteur',
        },
      ],
      docs: [
        {
          title: 'Analyse laboratoire',
          link:
            'https://www.fichier-pdf.fr/2019/08/26/liste-des-analyses/preview-liste-des-analyses-1.jpg',
        },
      ],
      prescription: [
        {
          name: 'ABILIFY 10 MG',
          quantity: 1,
          description: 'apres les 3 repas',
        },
        {
          name: 'DEFAX 30 MG',
          quantity: 2,
          description: 'avant dormir',
        },
      ],
    };
    let patients = [],
      doctors = [],
      receptionists = [];
    let hospital = dummy(Hospital, {
      ignore: [],
      returnDate: true,
      autoDetect: true,
    });
    let types = [];
    hospital.name = 'Cabinet Médical DR Moors';
    hospital.address =
      'Boulevard Abou Chouaib Doukkali 210 etage 1 appartement 2، Casablanca 20320';
    hospital.localPhone = '+212-538-152-542';
    hospital.phoneNumber = '+212-656-721-928';
    hospital.fax = '+212-656-721-928';
    hospital.type = 'CLINICAL';
    hospital.sessions = [];
    hospital.avgSessionDuration = 20;
    hospital.country = 'morocco';
    hospital.currency = 'MAD';
    hospital.language = 'fr';

    hospital.schedules = [1, 2, 3, 4, 5, 6, 0].map((x) => {
      return {
        startTime: '09:00',
        endTime: '17:00',
        startBreak: '13:00',
        endBreak: '14:00',
        day: x,
      };
    });

    hospital = new Hospital(hospital);
    hospital = await hospital.save();
    hospitalIDs.push(hospital._id);
    //Rooms
    let roomsNumber = 1;
    let rooms = [];
    for (let r = 0; r < roomsNumber; r++) {
      let room = dummy(Room, {
        ignore: ['horaire', 'updatedBy', 'createdBy'],
        returnDate: true,
        autoDetect: true,
      });
      room.roomNumber = r + 1;
      room.hospital = hospital._id;
      room = new Room(room);
      room = await room.save();
      rooms.push(room);
    }
    //Specialty
    let specialties = [];
    let specialtiesNumber = 3;
    for (let s = 0; s < specialtiesNumber; s++) {
      let specialty = dummy(Specialty, {
        ignore: ['practices', 'updatedBy', 'createdBy'],
        returnDate: true,
        autoDetect: true,
      });
      specialty.hospital = hospital._id;
      specialty = new Specialty(specialty);
      specialty = await specialty.save();
      specialties.push(specialty);
    }
    //doctors
    let doctorsNumber = 2;
    for (let p = 0; p < doctorsNumber; p++) {
      let profile = dummy(Profile, {
        ignore: [
          'deletedAt',
          'updatedBy',
          'createdBy',
          'specialty',
          'profilePic',
          'doctors',
          'receptionits',
        ],
        returnDate: true,
        autoDetect: true,
      });
      profile = Object.assign(profile, regularProfile);
      if (p == 0) {
        profile.firstName = 'Amine';
        profile.lastName = 'ELALAOUI';
        profile.email = '<EMAIL>';
      }
      if (p == 1) {
        profile.firstName = 'Kamal';
        profile.lastName = 'ELMOURABIT';
        profile.email = '<EMAIL>';
        profile.isAdmin = true;
      }
      profile.specialty = specialties[rnd(0, 2)]._id;
      profile.title = 'DOCTOR';
      profile.position = 'SURGEON';
      profile.gender = 'MALE';
      profile.hospital = hospital._id;
      profile.birthDate = new Date(
        moment('2020-01-01')
          .add(-1 * rnd(10, 60) * 12, 'months')
          .toDate(),
      );
      profile = new Profile(profile);
      profile = await profile.save();
      doctors.push(profile);
      //user
      let user = dummy(User, {
        ignore: ['deletedAt', 'expiresIn'],
        returnDate: true,
        autoDetect: true,
      });
      if (p == 0) {
        user.levelOfAccess = 4;
        user.email = '<EMAIL>';
      }
      if (p == 1) {
        user.levelOfAccess = 4;
        user.email = '<EMAIL>';
      }
      user.password = '1234';
      user.profile = profile._id;
      user = new User(user);
      await user.save();
      //diagnoses
      if (p == 0) {
        let u = {profile: {hospital: {_id: hospital._id}}};
        await diagnoseService.initDiagnoses(u);
      }
    }
    hospital.doctors = doctors.map((x) => x._id);

    //receptionists
    let receptionistsNumber = 2;
    for (let p = 0; p < receptionistsNumber; p++) {
      let profile = dummy(Profile, {
        ignore: [
          'deletedAt',
          'updatedBy',
          'createdBy',
          'specialty',
          'profilePic',
          'doctors',
          'receptionits',
        ],
        returnDate: true,
        autoDetect: true,
      });
      profile = Object.assign(profile, regularProfile);
      if (p == 0) {
        profile.firstName = 'Rachida';
        profile.lastName = 'BOURIM';
      } else {
        profile.firstName = 'Nadira';
        profile.lastName = 'MOUSSAID';
      }

      profile.email = 'assistante' + p + '@gmail.com';

      profile.specialty = specialties[rnd(0, 2)]._id;
      profile.title = 'RECEPTIONIST';
      profile.position = 'REGULAR_RECEPTIONIST';
      profile.gender = 'FEMALE';
      profile.hospital = hospital._id;
      profile.birthDate = new Date(
        moment('2020-01-01')
          .add(-1 * rnd(10, 60) * 12, 'months')
          .toDate(),
      );

      profile = new Profile(profile);
      profile = await profile.save();
      receptionists.push(profile);

      //user
      let user = dummy(User, {
        ignore: ['deletedAt', 'expiresIn'],
        returnDate: true,
        autoDetect: true,
      });
      if (p == 0) {
        user.levelOfAccess = 4;
        user.email = '<EMAIL>';
      }
      if (p == 1) {
        user.levelOfAccess = 4;
        user.email = '<EMAIL>';
      }
      user.password = '1234';
      user.profile = profile._id;
      user = new User(user);
      await user.save();
    }
    hospital.receptionists = receptionists.map((x) => x._id);
    hospital.save();
    doctors[0].receptionists = [receptionists[0]._id];
    doctors[0].doctors = [doctors[0]._id];
    doctors[0] = await doctors[0].save();
    doctors[1].receptionists = [receptionists[0]._id, receptionists[1]._id];
    doctors[1].doctors = [doctors[0]._id, doctors[1]._id];
    doctors[1] = await doctors[1].save();
    receptionists[0].receptionists = [receptionists[0]._id];
    receptionists[0].doctors = [doctors[0]._id, doctors[1]._id];
    receptionists[0] = await receptionists[0].save();
    receptionists[1].receptionists = [receptionists[1]._id];
    receptionists[1].doctors = [doctors[1]._id];
    receptionists[1] = await receptionists[1].save();

    //patients
    let patientsNumber = 100;
    for (let p = 0; p < patientsNumber; p++) {
      let profile = dummy(Profile, {
        ignore: [
          'deletedAt',
          'updatedBy',
          'createdBy',
          'specialty',
          'profilePic',
          'speciality',
          'position',
          'residency',
          'seniority',
        ],
        returnDate: true,
        autoDetect: true,
      });
      profile = Object.assign(profile, regularProfile);

      profile.title = 'PATIENT';
      profile.hospital = hospital._id;
      profile.birthDate = new Date(
        moment('2020-01-01')
          .add(-1 * rnd(10, 60) * 12, 'months')
          .toDate(),
      );
      profile = new Profile(profile);
      profile = await profile.save();
      patients.push(profile);
    }

    //diagnoses
    let diagnosesNumber = 50;
    let diagnoses = await Diagnose.find();

    //supplies
    let suppliesNumber = 10;
    let supplies = [];
    let sessionTypes = [];
    for (let r = 0; r < suppliesNumber; r++) {
      let supply;
      if (r < 2) {
        supply = {
          hospital: hospital._id,
          name: r == 0 ? 'Consultation' : 'Controle',
          sellingPrice: 100,
          type: 'SESSION',
          avgDuration: r == 0 ? 30 : 20,
        };
      } else {
        supply = dummy(Supply, {
          ignore: ['consumedNumber'],
          returnDate: true,
          autoDetect: true,
        });
        supply.type = r % 2 == 0 ? 'DRUG' : 'MATERIAL';
        let drugs = [
          'Aiguille MOP',
          'Aiguille PDK',
          'Aiguille de phlébologie PIC',
          'Aiguille de mésothérapie PIC',
          'Aiguille de phlébologie BD Microlance 3',
          'Aiguille de prélèvement BD Vacutainer Eclipse',
          'Aiguilles hypodermiques Eurofine Euromedis',
          'Aiguilles hypodermiques Becton Dickinson - Microlance 3',
        ];
        let materials = [
          'Sonde Endovaginale E611-2',
          'Échographe Samsung',
          'Tensiomètre électronique ',
          'Brassard pour tensiomètre Spengler Mobi',
          'Tensiomètre',
          'Echographe portable DUS60 EDAN',
          'Bladder Scanner Vitascan DP',
          'Bladder scanner',
        ];

        supply.name = r % 2 == 0 ? drugs[rnd(0, 7)] : materials[rnd(0, 7)];
      }

      supply.hospital = hospital._id;
      supply.createdBy = receptionists[rnd(0, doctors.length - 1)]._id;
      supply.updatedBy = receptionists[rnd(0, doctors.length - 1)]._id;
      supply = new Supply(supply);
      supply = await supply.save();
      if (r < 2) {
        hospital.sessions.push(supply._id);
        await hospital.save();
        types.push(supply.name);
        sessionTypes.push(supply);
      }
      supplies.push(supply);
    }
    let appointments = [],
      sessions = [],
      invoices = [];
    //appointments
    for (var d = new Date(fDate); d <= new Date(tDate); d.setDate(d.getDate() + 1)) {
      for (var doctor = 0; doctor < doctors.length; doctor++) {
        d = new Date(d);
        let day = new Date(d).getDay();

        let appointmentsNumber = rnd(17, 50);
        for (let a = 0; a < appointmentsNumber; a++) {
          let startHospital = getDateFromTime(
            d,
            hospital.schedules.find((x) => x.day + '' == day + '').startTime,
          );
          let endHospital = getDateFromTime(
            d,
            hospital.schedules.find((x) => x.day + '' == day + '').endTime,
          );
          let appointment = dummy(Appointment, {
            ignore: ['fromSession', 'updatedBy', 'createdBy'],
            returnDate: true,
            autoDetect: true,
          });
          appointment = Object.assign(appointment, regularAppointment);

          appointment.description =
            a % 2 == 0
              ? 'de la part de docteur el aidi'
              : "une derniere séance avant l'operation";
          appointment.hospital = hospital._id;
          appointment.patient = patients[rnd(0, patients.length - 1)]._id;
          appointment.doctor = doctors[doctor]._id;
          appointment.type = types[a % 2];
          appointment.supply = sessionTypes[a % 2];
          appointment.createdBy = receptionists[rnd(0, receptionists.length - 1)]._id;
          appointment.updatedBy = receptionists[rnd(0, receptionists.length - 1)]._id;
          appointment.date = new Date(d);
          let between = rnd(5, 10);
          if (appointments[a - 1] && appointments[a - 1].startTime)
            appointments[a - 1].startTime = copyTime(d, appointments[a - 1].startTime);
          let startTime =
            a == 0
              ? startHospital
              : moment(appointments[a - 1].startTime)
                  .add(appointments[a - 1].supply.avgDuration, 'minutes')
                  .toDate();
          appointment.startTime = startTime;
          if (new Date(d).getTime() < new Date(getUtcDate(new Date())).getTime())
            appointment.state = 'COMPLETED';
          else if (a == appointmentsNumber - 1) appointment.state = 'CANCELED';
          else appointment.state = 'APPROVED';

          appointment = new Appointment(appointment);
          if (
            !appointments.some(
              (x) =>
                appointment.doctor + '' == x.doctor + '' &&
                new Date(d).getTime() == new Date(x.date).getTime() &&
                new Date(x.startTime) == new Date(startTime),
            )
          ) {
            if (
              a % 2 == 0 ||
              new Date(d).getTime() < new Date(getUtcDate(new Date())).getTime()
            ) {
              appointments.push(appointment);
            }
            //sessions
            if (appointment.state == 'COMPLETED') {
              appointment.waitingTime = 0;
              let session = dummy(Session, {
                ignore: [],
                returnDate: true,
                autoDetect: true,
              });
              between = rnd(1, 5);
              let retard = rnd(2, 6);
              session = Object.assign(session, regularSession);

              session.hospital = hospital._id;
              session.appointment = appointment._id;
              session.type = appointment.type;
              session.supply = appointment.supply;
              session.patient = appointment.patient;
              session.doctor = appointment.doctor;
              session.date = appointment.date;
              if (sessions[a - 1] && sessions[a - 1].endTime)
                sessions[a - 1].endTime = copyTime(d, sessions[a - 1].endTime);

              session.startTime =
                a == 0
                  ? moment(appointment.startTime).add(retard, 'minutes').toDate()
                  : moment(sessions[a - 1].endTime)
                      .add(retard, 'minutes')
                      .toDate();
              session.endTime = moment(session.startTime)
                .add(session.supply.avgDuration + between, 'minutes')
                .toDate();
              session.createdBy = session.doctor;
              session.updatedBy = session.doctor;
              session.room = rooms[rnd(0, rooms.length - 1)]._id;
              session.diagnoses = [];
              let rr = rnd(1, 3);
              for (let o = 0; o < rr; o++) {
                if (diagnoses.length - 1 > 1)
                  session.diagnoses.push(
                    diagnoses.filter(
                      (f) => !session.diagnoses.some((l) => l + '' == f._id + ''),
                    )[rnd(0, diagnoses.length - 1)]._id,
                  );
              }
              retard = rnd(0, 5);
              appointment.patientArrived = moment(appointment.startTime)
                .add(rnd(0, 5), 'minutes')
                .toDate();
              appointment.waitingTime = moment(new Date(session.startTime)).diff(
                moment(new Date(appointment.patientArrived)),
                'minutes',
              );
              if (
                a % 2 == 0 ||
                new Date(d).getTime() < new Date(getUtcDate(new Date())).getTime()
              ) {
                appointment = await appointment.save();
              }
              session = new Session(session);
              session = await session.save();
              sessions.push(session);
              if (
                new Date(endHospital).getTime() + rnd(0, 30) * 60 * 1000 <=
                new Date(moment(session.endTime).toDate()).getTime()
              ) {
                a = appointmentsNumber;
              }

              //invoice
              let invoice = dummy(Invoice, {
                ignore: [],
                returnDate: true,
                autoDetect: true,
              });
              await appointmentService.editStats(appointment._id, {
                profile: {hospital: hospital},
              });
              await appointmentService.updatePatientSessionsCount(
                appointment.patient._id || appointment.patient,
                {profile: {hospital: hospital}},
              );

              invoice.hospital = hospital._id;
              invoice.session = session._id;
              invoice.billingDate = session.endTime;
              invoice.paymentDate = invoice.billingDate;
              invoice.createdBy = session.doctor;
              invoice.updatedBy = appointment.createdBy;
              let supplyIndex = a % 2;
              invoice.items = [
                {
                  name: sessionTypes[supplyIndex].name,
                  description: session.supply.name,
                  price: sessionTypes[supplyIndex].sellingPrice,
                  quantity: 1,
                  tax: 0,
                  supply: sessionTypes[supplyIndex]._id,
                },
              ];
              invoice.total = sessionTypes[supplyIndex].sellingPrice;
              invoice.items.map((x) => {
                invoice.total = invoice.total + x.price * x.quantity * (1 + x.tax);
              });
              invoice.paid = invoice.total;
              invoice.appointment = appointment._id;

              invoice = new Invoice(invoice);
              invoice = await invoice.save();
              invoices.push(invoice);
            } else {
              appointment.waitingTime = 0;
              appointment.patientArrived = null;
              if (
                a % 2 == 0 ||
                new Date(d).getTime() < new Date(getUtcDate(new Date())).getTime()
              ) {
                appointment = await appointment.save();
              }
            }
          }

          if (
            new Date(endHospital).getTime() + rnd(0, 30) * 60 * 1000 <=
            new Date(
              moment(appointment.startTime)
                .add(appointment.supply.avgDuration + between, 'minutes')
                .toDate(),
            ).getTime()
          ) {
            a = appointmentsNumber;
          }
        }
      }
    }
  }
  let patients = await Profile.find({title: 'PATIENT', hospital: {$in: hospitalIDs}});
  let firstNamesMales = [
    'Radi',
    'Rafi',
    'Rafiq',
    'Raghib',
    'Rahman',
    "Ra'id",
    'Rais',
    'Rakin',
    'Rashad',
    'Rashid',
    'Ratib',
    'Rayhan',
    'Reda',
    'Ridwan',
    'Riyad',
    'Sabih',
    'Sabir',
    "Sa'd",
    'Sadaqat',
    "Sa'eed",
    'Safwan',
    'Salah',
    'Saleh',
    'Salim',
    'Salman',
    'Sameh',
    'Sami',
    'Samir',
    'Samman',
    'Saqr',
    'Sariyah',
    'Sayyar',
    'Sayyid',
    'Seif',
    'Shadi',
    'Shafiq',
    'Shakir',
    'Sharif',
    'Shihab',
    'Siraj',
    'Sofian',
    'Subhi',
    'Suhail',
    'Suhayb',
    'Sulaiman',
    "Su'ud",
    'Tahir',
    'Talal',
    'Talib',
    'Tamir',
    'Tammam',
    'Tamman',
    'Tarif',
    'Tariq',
    'Tawfiq',
    'Taymullah',
    'Taysir',
    'Tayyib',
    'Thabit',
    'Ubadah',
    'Ubaid',
    'Ubayy',
    'Umar',
    'Umarah',
    'Umayr',
    'Usama',
    'Utbah',
    'Uthman',
    'Wadi',
    'Wafid',
    'Wafiq',
    'Wahib',
    'Wahid',
    "Wa'il",
    'Wajih',
    'Wakil',
    'Waleed',
    'Walliyullah',
    'Wasim',
    'Wazir',
    'Yahyah',
    'Yaman',
    "Ya'qub",
    'Yasar',
    'Yasin',
    'Yasir',
    'Yazid',
    'Yunus',
    'Yusef',
    'Yushua',
    'Yusuf',
    'Zafar',
    'Zafir',
    'Zahid',
    'Zahir',
    'Zaid',
    'Zaim',
    'Zaki',
    'Zakiyy',
    'Ziyad',
    'Zubair',
    'Zuhair',
  ];
  let firstNamesFemales = [
    'ibtisam',
    'iibtahal',
    'abia',
    'arijawan',
    'arwah',
    'arij',
    'arihaan',
    'iisra',
    'asrar',
    'isead',
    'asliya',
    'iismahan',
    'asmaa',
    'uswa',
    'asil',
    'asima',
    'umat',
    'allah',
    'iishraq',
    'iishfaq',
    'ashwaq',
    'asala',
    'asila',
    'iiftakar',
    'afrah',
    'afkar',
    'afnan',
    'alhan',
    'altaf',
    'iilham',
    'alifa',
    'amal',
    'amani',
    'amina',
    'amnia',
    'amira',
    'amina',
    'iinaas',
    'iintsar',
    'anji',
    'iinsaf',
    'iineam',
    'anisa',
    'ayat',
    'iinas',
    'bariea',
    'bitala',
    'badawia',
    'badiea',
    'baraa',
    'birah',
    'baraeim',
    'biralnati',
    'barika',
    'barihan',
    'bariya',
    'bushraa',
    'basira',
    'balbala',
    'bnan',
    'binana',
    'binafsaj',
    'bahia',
    'bahija',
    'buran',
    'bayan',
    'bayda',
    'baysan',
    'bayda',
    'bayina',
    'tuhfa',
    'tahia',
    'tadhkar',
    'turath',
    'turkia',
    'tasamah',
    'tasbih',
    'tasnim',
    'taqa',
    'taqwaa',
    'tlal',
    'tamadar',
    'tahama',
    'tuhani',
    'tahnid',
    'tawhida',
    'tawadad',
    'tusil',
    'tawfiqa',
    'ty',
    'tayjan',
    'tima',
    'thabita',
    'thayira',
    'thara',
    'thana',
    'jala',
    'jamana',
    'jamila',
    'jiham',
    'juhara',
    'juria',
    'jawiria',
    'jayhan',
    'hakima',
    'habibatan',
    'hasna',
    'hisa',
    'hallaan',
    'hamida',
    'hanan',
    'hawara',
    'haya',
    'khatun',
    'khitam',
    'khadija',
    'khalud',
    'khawatir',
    'khawla',
    'khayria',
    'dan',
    'dania',
    'duriya',
    'duea',
    'daead',
    'dilal',
    'dima',
    'dhikraa',
    'raghida',
    'ramah',
    'ramy',
    'rania',
    'rawia',
    'rba',
    'rahab',
    'razan',
    'rsha',
    'radwaa',
    'rafif',
    'raqia',
    'ramzia',
    'riham',
    'rhf',
    'rawda',
    'ruea',
    'ruaa',
    'rim',
    'rima',
    'zakia',
    'zamarada',
    'zaynab',
    'sar',
    'sali',
    'sahar',
    'saluaa',
    'samahir',
    'samar',
    'smy',
    'sana',
    'sahaa',
    'sahir',
    'shadia',
    'shadhaa',
    'shamayil',
    'shima',
    'sabirin',
    'sabana',
    'eatika',
    'eablatan',
    'eabir',
    'eiza',
    'esmt',
    'eafaf',
    'eala',
    'eunud',
    'ghada',
    'ghazal',
    'ghayda',
    'fatan',
    'fatima',
    'fatahia',
    'fadawaa',
    'firial',
    'fahamia',
    'fawzia',
    'fayha',
    'kawthar',
    'labnaa',
    'lamaa',
    'luliwa',
    'laylaa',
    'majida',
    'muhasin',
    'maram',
    'marah',
    'marwa',
    'maryam',
    'mazina',
    'masara',
    'manal',
    'munaa',
    'manira',
    'maha',
    'may',
    'miada',
    'misa',
    'maysun',
    'nabigha',
    'nadia',
    'nabila',
    'najud',
    'nudaa',
    'narmin',
    'nshwa',
    'nghm',
    'nahaa',
    'nawal',
    'nuranaan',
    'nufa',
    'hala',
    'hiba',
    'hudana',
    'hdyl',
    'hla',
    'hanadi',
    'hind',
    'hifa',
    'wadad',
    'waead',
    'wala',
    'yumnaa',
  ];
  let lastNames = [
    'Abadi',
    'Abboud',
    'Almasi',
    'Amari',
    'Antar',
    'Antoun',
    'Arian',
    'Asfour',
    'Asghar',
    'Asker',
    'Aswad',
    'Atiyeh',
    'Attia',
    'Awad',
    'Baba',
    'Bahar',
    'Basara',
    'Baz',
    'Bishara',
    'Bitar',
    'Botros',
    'Boulos',
    'Boutros',
    'Cham',
    'Dagher',
    'Daher',
    'Deeb',
    'Essa',
    'Fakhoury',
    'Ganem',
    'Ganim',
    'Gerges',
    'Ghannam',
    'Guirguis',
    'Hadad',
    'Haddad',
    'Haik',
    'Hajjar',
    'Hakimi',
    'Halabi',
    'Hanania',
    'Handal',
    'Harb',
    'Isa',
    'Issa',
    'Kalb',
    'Kanaan',
    'Kassab',
    'Kassis',
    'Kattan',
    'Khouri',
    'Khoury',
    'Kouri',
    'Koury',
    'Maalouf',
    'Maloof',
    'Malouf',
    'Maroun',
    'Masih',
    'Mifsud',
    'Mikhail',
    'Moghadam',
    'Morcos',
    'Nader',
    'Nahas',
    'Naifeh',
    'Najjar',
    'Naser',
    'Nassar',
    'Nazari',
    'Pagination',
    'Quraishi',
    'Qureshi',
    'Rahal',
    'Rahal',
    'Sabbag',
    'Sabbagh',
    'Safar',
    'Said',
    'Salib',
    'Saliba',
    'Samaha',
    'Sarraf',
    'Sayegh',
    'Seif',
    'Shadid',
    'Shalhoub',
    'Shammas',
    'Shamon',
    'Shamoon',
    'Shamoun',
    'Sleiman',
    'Tahan',
    'Tannous',
    'Toma',
    'Totah',
    'Touma',
    'Tuma',
    'Wasem',
    'Zogby',
  ];

  patients.map((x, i) => {
    if (x.gender == 'MALE') x.firstName = firstNamesMales[i];
    else x.firstName = firstNamesFemales[i];
    x.lastName = lastNames[i];
    x.save();
  });
};
