import mongoose from "mongoose";
import mongoosePaginate from 'mongoose-paginate';

const Schema = mongoose.Schema;

const SuperAdminSchema = new mongoose.Schema({
    hospital: {
        type: Schema.Types.ObjectId,
        ref: 'Hospital'
    },
    deletedAt: {
        type: Date,
        default: null
    },
    updatedBy: {
        type: Schema.Types.ObjectId,
        ref: 'Profile'
    },
    createdBy: {
        type: Schema.Types.ObjectId,
        ref: 'Profile'
    },
    profile:{
        type: Schema.Types.ObjectId,
        ref: 'Profile',
    }

}, {
    timestamps: true,
    collation: { locale: 'fr', strength: 1 }
});

SuperAdminSchema.pre('find', softDeleteMiddleware);
SuperAdminSchema.pre('findOne', softDeleteMiddleware);
SuperAdminSchema.pre('sort', softDeleteMiddleware);
SuperAdminSchema.plugin(mongoosePaginate);

SuperAdminSchema.statics.restore = function (query, callback) {
    return this.updateMany(query, { $set: { deletedAt: null } }, callback);
};

SuperAdminSchema.statics.softDelete = function (query, callback) {
    return this.findOneAndUpdate(query, { $set: { deletedAt: Date.now() } }, { new: true }, callback);
};

SuperAdminSchema.statics.softDeleteMany = function (query, callback) {
    return this.updateMany(query, { $set: { deletedAt: Date.now() } }, { new: true }, callback);
};

function softDeleteMiddleware(next) {
    var filter = this.getQuery();
    filter.deletedAt = null;
    next();
}
module.exports = mongoose.model('SuperAdmin', SuperAdminSchema);
