import Controller from './Controller';
import SupplyService from "../services/SupplyService";
import Supply from "../models/Supply";
const supplyService = new SupplyService(Supply);

class SupplyController extends Controller {

  constructor(service) {
    super(service);
    this.getSupplies = this.getSupplies.bind(this);
    this.createOrUpdateSupply = this.createOrUpdateSupply.bind(this);
    this.deleteSupplies = this.deleteSupplies.bind(this);

  }
  async getSupplies(req) {
    return supplyService.getSupplies(req.body, req.user);
  }
  async createOrUpdateSupply(req) {
    return supplyService.createOrUpdateSupply(req.body.supply, req.body.supplyID, req.user);
  }
  async deleteSupplies(req) {
    return supplyService.deleteSupplies( req.body, req.user);
  }

}

export default new SupplyController(supplyService);