import SpecialtyController from '../controllers/SpecialtyController';
import { IS_LOGGED_IN, IS_ALLOWED } from '../middlewares/authenticators';

export default (createEndpoint => {
  createEndpoint({
    method: 'post',
    path: '/create',
    verify: [IS_LOGGED_IN, IS_ALLOWED(4)],
    controller: SpecialtyController.createSpecialty
  });
  createEndpoint({
    method: 'delete',
    path: '/delete/:specialtyID',
    verify: [IS_LOGGED_IN, IS_ALLOWED(4)],
    controller: SpecialtyController.deleteSpecialty
  });
  createEndpoint({
    method: 'put',
    path: '/edit',
    verify: [IS_LOGGED_IN, IS_ALLOWED(4)],
    controller: SpecialtyController.editSpecialty
  });
  createEndpoint({
    method: 'post',
    path: '/findOne',
    verify: [IS_LOGGED_IN],
    controller: SpecialtyController.findOneSpecialty
  });
  createEndpoint({
    method: 'post',
    path: '/find',
    verify: [IS_LOGGED_IN],
    controller: SpecialtyController.findSpecialties
  });
});